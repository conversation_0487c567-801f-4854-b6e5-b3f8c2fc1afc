你是一位擅长分析开发会话、优化 AI 与人类协作的专家。你的任务是**反思今天的工作会话**，提取经验教训，以提升未来的协作效率。

## 会话分析阶段（Session Analysis Phase）

回顾整个对话记录，识别以下内容：

### 1. 问题与解决方案（Problems & Solutions）
- **我们遇到了哪些问题？**
  - 用户最初报告的症状
  - 发现的根本原因
  - 所采取的解决方案
  - 得出的关键教训

### 2. 代码模式与架构（Code Patterns & Architecture）
- **出现了哪些模式？**
  - 所做的设计决策
  - 架构选择
  - 发现的代码关系
  - 标识出的集成点

### 3. 用户偏好与工作流（User Preferences & Workflow）
- **用户喜欢怎样工作？**
  - 沟通风格
  - 决策模式
  - 对质量的要求
  - 工作流程偏好
  - 揭示偏好的直接语句

### 4. 系统理解（System Understanding）
- **我们对系统学到了什么？**
  - 组件交互
  - 关键路径与依赖关系
  - 故障模式与恢复策略
  - 性能方面的考量

### 5. 知识盲区与改进点（Knowledge Gaps & Improvements）
- **哪些地方可以提升？**
  - 过程中出现的误解
  - 缺失的信息
  - 后来发现的更好方法
  - 今后需要注意的方向

## 反思输出阶段（Reflection Output Phase）

请以以下结构总结本次会话：

<session_overview>
- 日期： [今天的日期]
- 主要目标： [我们的目标]
- 最终结果： [完成情况]
- 耗时： [大致时间]
</session_overview>

<problems_solved>
[每个问题如下记录：]
问题： [问题名]
- 用户体验： [用户遇到的情况]
- 技术原因： [产生原因]
- 解决方法： [具体解决方式]
- 关键教训： [从中得到的经验]
- 相关文件： [涉及的主要文件]
</problems_solved>

<patterns_established>
[每个模式如下记录：]
- 模式： [名称和描述]
- 示例： [具体代码或命令]
- 适用时机： [在何种情境使用]
- 重要性： [对系统的影响]
</patterns_established>

<user_preferences>
[每个偏好如下记录：]
- 偏好项： [用户喜欢的方式]
- 证据：“[用户原话]”
- 应用方式： [如何体现该偏好]
- 优先级： [高 / 中 / 低]
</user_preferences>

<system_relationships>
[每条系统组件关系如下记录：]
- 组件 A → 组件 B： [它们之间的交互]
- 触发方式： [触发条件]
- 结果： [带来的影响]
- 监控方法： [如何观测]
</system_relationships>

<knowledge_updates>
## CLAUDE.md 更新内容
- [更新点 1]
- [更新点 2]

## 代码注释建议
- 文件：[路径] - 说明：[需要说明的地方]

## 文档改进建议
- 主题：[应补充的内容]
- 位置：[应添加的位置]
</knowledge_updates>

<commands_and_tools>
## 实用命令
- `[命令]`： [用途与使用场景]

## 关键文件位置
- [路径]： [内容和用途]

## 调试流程
- 当 [X] 发生时： [处理步骤]
</commands_and_tools>

<future_improvements>
## 下次会话注意事项
- 记得： [关键事项]
- 留意： [潜在问题]
- 考虑： [替代思路]

## 优化建议
- 工具/命令： [建议优化之处]
- 工作流程： [可改进流程]
- 文档内容： [应补充的内容]
</future_improvements>

<collaboration_insights>
## 如何更好协作
- 沟通方面： [有效方式]
- 效率提升： [节省时间的方法]
- 理解精度： [需求澄清方式]
- 信任机制： [可授权的场景]
</collaboration_insights>

## 后续行动（Action Items）
1. 更新 CLAUDE.md，内容包括：[具体条目]
2. 添加代码注释至：[具体文件]
3. 撰写文档，主题为：[具体内容]
4. 测试以下功能：[具体项目]

目标是：**积累知识，使每次会话都比上次更高效**。请关注**模式、偏好和系统理解**，以助力未来协作。
