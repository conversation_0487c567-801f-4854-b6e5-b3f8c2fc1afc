#!/usr/bin/env python3
"""
字段完整性测试脚本 - 验证修复后的字段映射是否完整

测试目标：
1. 验证_get_basic_video_info返回的字段数量和名称
2. 验证_process_video_detail中的字段映射是否完整
3. 对比原有的23个字段是否都已恢复

运行方式：
python test/field_test.py
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from main import DouyinScraper


def test_field_completeness():
    """测试字段完整性"""
    print("=" * 60)
    print("字段完整性测试")
    print("=" * 60)
    
    scraper = DouyinScraper()
    
    # 期望的23个字段（从CSV文件头部获取）
    expected_fields = [
        '分享数', '创建时间', '播放数', '收藏数', '点赞数', '视频id', 
        '视频作者UID', '视频作者企业认证原因', '视频作者关注数', '视频作者头像', 
        '视频作者总被赞数', '视频作者抖音号', '视频作者昵称', '视频作者签名', 
        '视频作者粉丝数', '视频作者获赞数', '视频作者认证信息', '视频描述', 
        '视频时长', '视频链接', '评论数', '音乐作者', '音乐标题'
    ]
    
    print(f"期望字段数量: {len(expected_fields)}")
    print("期望字段列表:")
    for i, field in enumerate(expected_fields, 1):
        print(f"  {i:2d}. {field}")
    
    print("\n" + "-" * 60)
    
    # 测试_get_basic_video_info方法
    basic_info = scraper._get_basic_video_info("test_video_id")
    actual_fields = list(basic_info.keys())
    
    print(f"_get_basic_video_info 返回字段数量: {len(actual_fields)}")
    print("实际字段列表:")
    for i, field in enumerate(actual_fields, 1):
        print(f"  {i:2d}. {field}")
    
    print("\n" + "-" * 60)
    
    # 检查字段完整性
    missing_fields = set(expected_fields) - set(actual_fields)
    extra_fields = set(actual_fields) - set(expected_fields)
    
    if missing_fields:
        print("❌ 缺失的字段:")
        for field in missing_fields:
            print(f"  - {field}")
    else:
        print("✅ 没有缺失字段")
    
    if extra_fields:
        print("ℹ️  额外的字段:")
        for field in extra_fields:
            print(f"  + {field}")
    
    print("\n" + "-" * 60)
    
    # 测试_process_video_detail方法的字段映射
    print("测试 _process_video_detail 字段映射:")
    
    # 模拟视频详情数据结构
    mock_video_detail = {
        'aweme_id': 'test_id',
        'desc': 'test description',
        'create_time': 1234567890,
        'duration': 30000,
        'author': {
            'uid': 'test_uid',
            'nickname': 'test_nickname',
            'unique_id': 'test_unique_id',
            'avatar_thumb': {'url_list': ['test_avatar_url']},
            'signature': 'test_signature',
            'follower_count': 1000,
            'following_count': 100,
            'favoriting_count': 50,
            'total_favorited': 5000,
            'custom_verify': 'test_verify',
            'enterprise_verify_reason': 'test_enterprise'
        },
        'statistics': {
            'digg_count': 100,
            'comment_count': 20,
            'share_count': 10,
            'collect_count': 5,
            'play_count': 1000
        },
        'music': {
            'title': 'test_music_title',
            'author': 'test_music_author'
        }
    }
    
    try:
        processed_info = scraper._process_video_detail(mock_video_detail, "test_video_id")
        processed_fields = list(processed_info.keys())
        
        print(f"_process_video_detail 返回字段数量: {len(processed_fields)}")
        
        # 检查处理后的字段完整性
        missing_processed = set(expected_fields) - set(processed_fields)
        if missing_processed:
            print("❌ _process_video_detail 缺失的字段:")
            for field in missing_processed:
                print(f"  - {field}")
        else:
            print("✅ _process_video_detail 字段完整")
            
    except Exception as e:
        print(f"❌ _process_video_detail 测试失败: {e}")
    
    print("\n" + "=" * 60)
    
    # 总结
    if len(actual_fields) == len(expected_fields) and not missing_fields:
        print("🎉 字段完整性测试通过！所有23个字段都已恢复。")
    else:
        print(f"⚠️  字段完整性测试未完全通过。期望{len(expected_fields)}个字段，实际{len(actual_fields)}个字段。")
    
    print("=" * 60)


def main():
    """主函数"""
    test_field_completeness()


if __name__ == "__main__":
    main()
