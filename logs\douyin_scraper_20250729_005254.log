2025-07-29 00:52:54 - BatchMain - INFO - ============================================================
2025-07-29 00:52:54 - BatchMain - INFO - 抖音数据批量抓取程序启动
2025-07-29 00:52:54 - Bat<PERSON><PERSON>ain - INFO - ============================================================
2025-07-29 00:52:54 - BatchMain - INFO - 批量处理配置 - 间隔: 5s, 重试: 2次, 跳过失败: True
2025-07-29 00:52:54 - BatchMain - INFO - 计划处理 2 个抖音ID
2025-07-29 00:52:54 - BatchMain - INFO - 
[1/2] 开始处理抖音ID: 96967475948
2025-07-29 00:52:54 - <PERSON><PERSON><PERSON>ain - INFO - 开始处理用户 - 抖音ID: 96967475948
2025-07-29 00:52:54 - BatchMain - INFO - 配置参数 - 最大粉丝数: 30, 最大喜欢数: 20
2025-07-29 00:52:54 - DouyinScraper - INFO - 开始获取用户 sec_uid - 抖音ID: 96967475948
2025-07-29 00:52:56 - DouyinScraper - INFO - 成功获取 sec_uid: MS4wLjABAAAA8LvNkjqN2KMfdvLEmYvncJbNDlLxPG6JVb7a525CCd6AoM6RX-8KymWDtmV8gd3V - 抖音ID: 96967475948
2025-07-29 00:52:56 - BatchMain - INFO - 获取到 sec_uid: MS4wLjABAAAA8LvNkjqN2KMfdvLEmYvncJbNDlLxPG6JVb7a525CCd6AoM6RX-8KymWDtmV8gd3V
2025-07-29 00:52:56 - BatchMain - INFO - 开始获取用户基本信息
2025-07-29 00:52:59 - BatchMain - INFO - 用户基本信息获取完成
2025-07-29 00:53:02 - DouyinScraper - WARNING - 首包响应中无 followers 字段, 重试: 1
2025-07-29 00:53:04 - BatchMain - INFO - 用户 96967475948 处理完成，耗时: 9.85秒
2025-07-29 00:53:04 - BatchMain - INFO - 等待 5 秒后处理下一个ID...
2025-07-29 00:53:09 - BatchMain - INFO - 
[2/2] 开始处理抖音ID: testid123
2025-07-29 00:53:09 - BatchMain - INFO - 开始处理用户 - 抖音ID: testid123
2025-07-29 00:53:09 - BatchMain - INFO - 配置参数 - 最大粉丝数: 30, 最大喜欢数: 20
2025-07-29 00:53:09 - DouyinScraper - INFO - 开始获取用户 sec_uid - 抖音ID: testid123
2025-07-29 00:53:12 - DouyinScraper - ERROR - 未找到 sec_uid - 抖音ID: testid123
2025-07-29 00:53:12 - BatchMain - ERROR - 用户 testid123 处理失败: 未在 suggest_words 响应中找到 sec_uid。
Traceback (most recent call last):
  File "D:\Desk\douyin\dp_douyin\main.py", line 64, in process_single_douyin_id
    sec_uid = scraper.fetch_sec_uid(douyin_id)
  File "D:\Desk\douyin\dp_douyin\douyin.py", line 286, in fetch_sec_uid
    raise RuntimeError("未在 suggest_words 响应中找到 sec_uid。")
RuntimeError: 未在 suggest_words 响应中找到 sec_uid。
2025-07-29 00:53:12 - BatchMain - WARNING - 处理 testid123 失败，准备重试: 未在 suggest_words 响应中找到 sec_uid。
2025-07-29 00:53:14 - BatchMain - INFO - 第 1 次重试处理 testid123
2025-07-29 00:53:14 - BatchMain - INFO - 开始处理用户 - 抖音ID: testid123
2025-07-29 00:53:14 - BatchMain - INFO - 配置参数 - 最大粉丝数: 30, 最大喜欢数: 20
2025-07-29 00:53:14 - DouyinScraper - INFO - 开始获取用户 sec_uid - 抖音ID: testid123
2025-07-29 00:53:16 - DouyinScraper - ERROR - 未找到 sec_uid - 抖音ID: testid123
2025-07-29 00:53:16 - BatchMain - ERROR - 用户 testid123 处理失败: 未在 suggest_words 响应中找到 sec_uid。
Traceback (most recent call last):
  File "D:\Desk\douyin\dp_douyin\main.py", line 64, in process_single_douyin_id
    sec_uid = scraper.fetch_sec_uid(douyin_id)
  File "D:\Desk\douyin\dp_douyin\douyin.py", line 286, in fetch_sec_uid
    raise RuntimeError("未在 suggest_words 响应中找到 sec_uid。")
RuntimeError: 未在 suggest_words 响应中找到 sec_uid。
2025-07-29 00:53:16 - BatchMain - WARNING - 处理 testid123 失败，准备重试: 未在 suggest_words 响应中找到 sec_uid。
2025-07-29 00:53:18 - BatchMain - INFO - 第 2 次重试处理 testid123
2025-07-29 00:53:18 - BatchMain - INFO - 开始处理用户 - 抖音ID: testid123
2025-07-29 00:53:18 - BatchMain - INFO - 配置参数 - 最大粉丝数: 30, 最大喜欢数: 20
2025-07-29 00:53:18 - DouyinScraper - INFO - 开始获取用户 sec_uid - 抖音ID: testid123
2025-07-29 00:53:20 - DouyinScraper - ERROR - 未找到 sec_uid - 抖音ID: testid123
2025-07-29 00:53:20 - BatchMain - ERROR - 用户 testid123 处理失败: 未在 suggest_words 响应中找到 sec_uid。
Traceback (most recent call last):
  File "D:\Desk\douyin\dp_douyin\main.py", line 64, in process_single_douyin_id
    sec_uid = scraper.fetch_sec_uid(douyin_id)
  File "D:\Desk\douyin\dp_douyin\douyin.py", line 286, in fetch_sec_uid
    raise RuntimeError("未在 suggest_words 响应中找到 sec_uid。")
RuntimeError: 未在 suggest_words 响应中找到 sec_uid。
2025-07-29 00:53:20 - BatchMain - WARNING - ID testid123 处理失败，跳过并继续处理下一个
2025-07-29 00:53:20 - BatchMain - INFO - 批量处理报告已保存: D:\Desk\douyin\dp_douyin\data\batch_report_20250729_005320.json
2025-07-29 00:53:20 - BatchMain - INFO - ============================================================
2025-07-29 00:53:20 - BatchMain - INFO - 抖音数据批量抓取程序结束
2025-07-29 00:53:20 - BatchMain - INFO - ============================================================
