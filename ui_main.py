#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
抖音数据抓取器 - 主UI界面
基于Qt6的现代化图形界面
"""

import sys
import os
import json
from datetime import datetime
from typing import Dict, List, Optional

from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
    QSplitter, QTabWidget, QGroupBox, QLabel, QLineEdit, QSpinBox,
    QPushButton, QCheckBox, QComboBox, QProgressBar, QTextEdit,
    QTableWidget, QTableWidgetItem, QHeaderView, QFrame,
    QGridLayout, QScrollArea, QMessageBox, QFileDialog, QDialog
)
from PyQt6.QtCore import (
    Qt, QThread, pyqtSignal, QTimer, QSize
)
from PyQt6.QtGui import (
    QFont, QIcon, QPalette, QColor, QPixmap
)

# 导入后端逻辑
from douyin import DouyinScraper
from utils import (
    load_config, json_to_csv, get_data_file_path, 
    get_douyin_ids_from_config, is_batch_mode_enabled,
    BatchProcessingReport, generate_batch_filename
)
from logger import setup_logger
import main  # 导入批量处理逻辑


class ModernButton(QPushButton):
    """现代化按钮样式"""
    def __init__(self, text: str, button_type: str = "primary"):
        super().__init__(text)
        self.button_type = button_type
        self.setMinimumHeight(35)
        self.apply_style()
    
    def apply_style(self):
        if self.button_type == "primary":
            self.setStyleSheet("""
                QPushButton {
                    background-color: #2563eb;
                    color: white;
                    border: none;
                    border-radius: 6px;
                    padding: 8px 16px;
                    font-weight: 500;
                }
                QPushButton:hover {
                    background-color: #1d4ed8;
                }
                QPushButton:pressed {
                    background-color: #1e40af;
                }
                QPushButton:disabled {
                    background-color: #9ca3af;
                }
            """)
        elif self.button_type == "success":
            self.setStyleSheet("""
                QPushButton {
                    background-color: #059669;
                    color: white;
                    border: none;
                    border-radius: 6px;
                    padding: 8px 16px;
                    font-weight: 500;
                }
                QPushButton:hover {
                    background-color: #047857;
                }
                QPushButton:pressed {
                    background-color: #065f46;
                }
            """)
        elif self.button_type == "danger":
            self.setStyleSheet("""
                QPushButton {
                    background-color: #dc2626;
                    color: white;
                    border: none;
                    border-radius: 6px;
                    padding: 8px 16px;
                    font-weight: 500;
                }
                QPushButton:hover {
                    background-color: #b91c1c;
                }
                QPushButton:pressed {
                    background-color: #991b1b;
                }
            """)


class CaptchaDialog(QDialog):
    """滑块验证提示对话框"""
    
    def __init__(self, scene: str, parent=None):
        super().__init__(parent)
        self.scene = scene
        self.user_cancelled = False
        self.init_ui()
        
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("滑块验证")
        self.setModal(True)
        self.setFixedSize(450, 300)
        
        # 设置窗口样式
        self.setStyleSheet("""
            QDialog {
                background-color: #ffffff;
                border: 1px solid #d1d5db;
                border-radius: 8px;
            }
        """)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # 标题
        title = QLabel("检测到滑块验证")
        title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #dc2626;
                text-align: center;
            }
        """)
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)
        
        # 场景描述
        scene_label = QLabel(f"验证场景: {self.scene}")
        scene_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #374151;
                background-color: #f3f4f6;
                padding: 8px 12px;
                border-radius: 6px;
                border: 1px solid #d1d5db;
            }
        """)
        layout.addWidget(scene_label)
        
        # 操作说明
        instructions = QLabel(
            "请按照以下步骤完成验证：\n\n"
            "1. 切换到已打开的浏览器窗口\n"
            "2. 完成页面上的滑块验证\n"
            "3. 验证成功后返回此对话框\n"
            "4. 点击「已完成验证」按钮继续"
        )
        instructions.setStyleSheet("""
            QLabel {
                font-size: 13px;
                color: #4b5563;
                line-height: 1.4;
                padding: 15px;
                background-color: #f8fafc;
                border-radius: 6px;
                border-left: 4px solid #2563eb;
            }
        """)
        layout.addWidget(instructions)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)
        
        # 取消按钮
        cancel_btn = ModernButton("取消任务", "danger")
        cancel_btn.clicked.connect(self.on_cancel_clicked)
        button_layout.addWidget(cancel_btn)
        
        # 完成按钮
        complete_btn = ModernButton("已完成验证", "success")
        complete_btn.clicked.connect(self.on_complete_clicked)
        button_layout.addWidget(complete_btn)
        
        layout.addLayout(button_layout)
        
        # 让完成按钮获得焦点
        complete_btn.setFocus()
    
    def on_complete_clicked(self):
        """用户点击完成验证"""
        self.user_cancelled = False
        self.accept()
    
    def on_cancel_clicked(self):
        """用户点击取消任务"""
        self.user_cancelled = True
        self.reject()
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        self.user_cancelled = True
        super().closeEvent(event)


class ConfigPanel(QWidget):
    """配置面板"""
    def __init__(self):
        super().__init__()
        self.config = load_config()
        self.init_ui()
        self.load_config_values()
    
    def init_ui(self):
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 标题
        title = QLabel("配置设置")
        title.setStyleSheet("""
            QLabel {
                font-size: 18px; 
                font-weight: bold; 
                color: #1f2937; 
                padding: 20px;
                background-color: #f8fafc;
                border-bottom: 1px solid #e2e8f0;
            }
        """)
        main_layout.addWidget(title)
        
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: #f8fafc;
            }
            QScrollBar:vertical {
                background-color: #f1f5f9;
                width: 8px;
                border-radius: 4px;
            }
            QScrollBar::handle:vertical {
                background-color: #cbd5e1;
                border-radius: 4px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #94a3b8;
            }
        """)
        
        # 滚动内容容器
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)
        scroll_layout.setSpacing(20)
        scroll_layout.setContentsMargins(20, 20, 20, 20)
        
        # 基础配置组
        self.create_basic_config_group(scroll_layout)
        
        # 抓取限制组
        self.create_limits_group(scroll_layout)
        
        # 性能优化组
        self.create_optimization_group(scroll_layout)
        
        # 批量处理设置组
        self.create_batch_config_group(scroll_layout)
        
        # 导出设置组
        self.create_export_group(scroll_layout)
        
        # 添加弹性空间，让内容顶部对齐
        scroll_layout.addStretch()
        
        # 保存配置按钮
        save_btn = ModernButton("保存配置", "success")
        save_btn.clicked.connect(self.save_config)
        scroll_layout.addWidget(save_btn)
        
        # 设置滚动区域
        scroll_area.setWidget(scroll_content)
        main_layout.addWidget(scroll_area)
    
    def create_basic_config_group(self, parent_layout):
        """创建基础配置组"""
        group = QGroupBox("基础配置")
        group.setStyleSheet(self.get_group_style())
        
        layout = QVBoxLayout(group)
        layout.setSpacing(15)
        
        # 批量模式开关
        batch_mode_layout = QHBoxLayout()
        batch_mode_layout.setSpacing(10)
        
        self.enable_batch_mode = QCheckBox("启用批量处理模式")
        self.enable_batch_mode.setStyleSheet(self.get_checkbox_style())
        self.enable_batch_mode.toggled.connect(self.on_batch_mode_toggled)
        batch_mode_layout.addWidget(self.enable_batch_mode)
        
        batch_mode_layout.addStretch()
        layout.addLayout(batch_mode_layout)
        
        # 抖音ID输入区域
        id_layout = QVBoxLayout()
        id_layout.setSpacing(5)
        
        # 单个ID输入（兼容旧模式）
        self.single_id_label = QLabel("目标抖音ID:")
        id_layout.addWidget(self.single_id_label)
        
        self.douyin_id_input = QLineEdit()
        self.douyin_id_input.setPlaceholderText("请输入要抓取的抖音用户ID")
        self.douyin_id_input.setMinimumHeight(35)
        self.douyin_id_input.setStyleSheet("""
            QLineEdit {
                padding: 8px 12px;
                border: 1px solid #d1d5db;
                border-radius: 6px;
                font-size: 14px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #2563eb;
                outline: none;
            }
        """)
        id_layout.addWidget(self.douyin_id_input)
        
        # 批量ID输入（新功能）
        self.batch_id_label = QLabel("批量抖音ID列表（每行一个ID）:")
        id_layout.addWidget(self.batch_id_label)
        
        self.batch_id_input = QTextEdit()
        self.batch_id_input.setPlaceholderText("每行输入一个抖音ID，例如：\n96967475948\n123456789\n987654321")
        self.batch_id_input.setMaximumHeight(120)
        self.batch_id_input.setStyleSheet("""
            QTextEdit {
                padding: 8px 12px;
                border: 1px solid #d1d5db;
                border-radius: 6px;
                font-size: 14px;
                background-color: white;
                font-family: 'Consolas', 'Monaco', monospace;
            }
            QTextEdit:focus {
                border-color: #2563eb;
                outline: none;
            }
        """)
        id_layout.addWidget(self.batch_id_input)
        
        layout.addLayout(id_layout)
        
        # 参数设置网格布局
        params_layout = QGridLayout()
        params_layout.setSpacing(15)
        
        # JS执行超时
        params_layout.addWidget(QLabel("JS执行超时(秒):"), 0, 0)
        self.js_timeout_input = QSpinBox()
        self.js_timeout_input.setRange(3, 60)
        self.js_timeout_input.setValue(10)
        self.js_timeout_input.setMinimumHeight(35)
        self.js_timeout_input.setStyleSheet(self.get_spinbox_style())
        params_layout.addWidget(self.js_timeout_input, 0, 1)
        
        # 重试次数
        params_layout.addWidget(QLabel("重试次数:"), 1, 0)
        self.js_retry_input = QSpinBox()
        self.js_retry_input.setRange(1, 10)
        self.js_retry_input.setValue(3)
        self.js_retry_input.setMinimumHeight(35)
        self.js_retry_input.setStyleSheet(self.get_spinbox_style())
        params_layout.addWidget(self.js_retry_input, 1, 1)
        
        layout.addLayout(params_layout)
        parent_layout.addWidget(group)
    
    def create_limits_group(self, parent_layout):
        """创建抓取限制组"""
        group = QGroupBox("抓取限制")
        group.setStyleSheet(self.get_group_style())
        
        layout = QGridLayout(group)
        layout.setSpacing(15)
        
        # 最大粉丝数量
        layout.addWidget(QLabel("最大粉丝数量:"), 0, 0)
        self.max_follower_input = QSpinBox()
        self.max_follower_input.setRange(1, 10000)
        self.max_follower_input.setValue(30)
        self.max_follower_input.setMinimumHeight(35)
        self.max_follower_input.setStyleSheet(self.get_spinbox_style())
        layout.addWidget(self.max_follower_input, 0, 1)
        
        # 最大喜欢数量
        layout.addWidget(QLabel("最大喜欢数量:"), 1, 0)
        self.max_favorite_input = QSpinBox()
        self.max_favorite_input.setRange(1, 1000)
        self.max_favorite_input.setValue(20)
        self.max_favorite_input.setMinimumHeight(35)
        self.max_favorite_input.setStyleSheet(self.get_spinbox_style())
        layout.addWidget(self.max_favorite_input, 1, 1)
        
        parent_layout.addWidget(group)
    
    def create_optimization_group(self, parent_layout):
        """创建性能优化组"""
        group = QGroupBox("性能优化")
        group.setStyleSheet(self.get_group_style())
        
        layout = QVBoxLayout(group)
        layout.setSpacing(15)
        
        # 优化阶段固定为完整优化
        optimization_info = QLabel("优化模式: 完整优化 (推荐)")
        optimization_info.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #059669;
                font-weight: bold;
                padding: 8px 12px;
                background-color: #f0fdf4;
                border: 1px solid #bbf7d0;
                border-radius: 6px;
            }
        """)
        layout.addWidget(optimization_info)
        
        # 并发设置
        concurrent_layout = QVBoxLayout()
        concurrent_layout.setSpacing(10)
        
        self.enable_concurrent = QCheckBox("启用并发模式")
        self.enable_concurrent.setChecked(True)
        self.enable_concurrent.setStyleSheet(self.get_checkbox_style())
        concurrent_layout.addWidget(self.enable_concurrent)
        
        # 并发标签页数
        tabs_layout = QHBoxLayout()
        tabs_layout.addWidget(QLabel("并发标签页数:"))
        self.max_concurrent_tabs = QSpinBox()
        self.max_concurrent_tabs.setRange(1, 10)
        self.max_concurrent_tabs.setValue(5)
        self.max_concurrent_tabs.setMinimumHeight(35)
        self.max_concurrent_tabs.setStyleSheet(self.get_spinbox_style())
        tabs_layout.addWidget(self.max_concurrent_tabs)
        concurrent_layout.addLayout(tabs_layout)
        
        layout.addLayout(concurrent_layout)
        parent_layout.addWidget(group)
    
    def create_batch_config_group(self, parent_layout):
        """创建批量处理配置组"""
        self.batch_group = QGroupBox("批量处理设置")
        self.batch_group.setStyleSheet(self.get_group_style())
        
        layout = QGridLayout(self.batch_group)
        layout.setSpacing(15)
        
        # 批处理间隔时间
        layout.addWidget(QLabel("ID间隔时间(秒):"), 0, 0)
        self.batch_interval_input = QSpinBox()
        self.batch_interval_input.setRange(1, 60)
        self.batch_interval_input.setValue(5)
        self.batch_interval_input.setMinimumHeight(35)
        self.batch_interval_input.setStyleSheet(self.get_spinbox_style())
        layout.addWidget(self.batch_interval_input, 0, 1)
        
        # ID重试次数
        layout.addWidget(QLabel("ID重试次数:"), 1, 0)
        self.id_retry_input = QSpinBox()
        self.id_retry_input.setRange(0, 5)
        self.id_retry_input.setValue(2)
        self.id_retry_input.setMinimumHeight(35)
        self.id_retry_input.setStyleSheet(self.get_spinbox_style())
        layout.addWidget(self.id_retry_input, 1, 1)
        
        # 跳过失败ID
        self.skip_failed_ids = QCheckBox("跳过失败的ID继续处理")
        self.skip_failed_ids.setChecked(True)
        self.skip_failed_ids.setStyleSheet(self.get_checkbox_style())
        layout.addWidget(self.skip_failed_ids, 2, 0, 1, 2)
        
        # 生成批量报告
        self.generate_batch_report = QCheckBox("生成批量处理报告")
        self.generate_batch_report.setChecked(True)
        self.generate_batch_report.setStyleSheet(self.get_checkbox_style())
        layout.addWidget(self.generate_batch_report, 3, 0, 1, 2)
        
        # 文件命名模式
        layout.addWidget(QLabel("文件命名模式:"), 4, 0)
        self.naming_pattern = QComboBox()
        self.naming_pattern.addItems([
            "ID_时间戳 (推荐)",
            "时间戳_ID",
            "仅ID"
        ])
        self.naming_pattern.setCurrentIndex(0)
        self.naming_pattern.setMinimumHeight(35)
        self.naming_pattern.setStyleSheet("""
            QComboBox {
                padding: 8px 12px;
                border: 1px solid #d1d5db;
                border-radius: 6px;
                font-size: 14px;
                background-color: white;
            }
            QComboBox:focus {
                border-color: #2563eb;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #6b7280;
            }
        """)
        layout.addWidget(self.naming_pattern, 4, 1)
        
        parent_layout.addWidget(self.batch_group)
        
        # 初始隐藏批量设置组
        self.batch_group.setVisible(False)
    
    def create_export_group(self, parent_layout):
        """创建导出设置组"""
        group = QGroupBox("导出设置")
        group.setStyleSheet(self.get_group_style())
        
        layout = QVBoxLayout(group)
        layout.setSpacing(10)
        
        # 导出格式选择
        self.export_follower_json = QCheckBox("导出粉丝JSON")
        self.export_follower_csv = QCheckBox("导出粉丝CSV")
        self.export_favorite_json = QCheckBox("导出喜欢JSON")
        self.export_favorite_csv = QCheckBox("导出喜欢CSV")
        
        # 默认全选
        for checkbox in [self.export_follower_json, self.export_follower_csv,
                        self.export_favorite_json, self.export_favorite_csv]:
            checkbox.setChecked(True)
            checkbox.setStyleSheet(self.get_checkbox_style())
            layout.addWidget(checkbox)
        
        parent_layout.addWidget(group)
    
    def get_group_style(self):
        """获取组框样式"""
        return """
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                border: 2px solid #e5e7eb;
                border-radius: 8px;
                margin-top: 15px;
                padding-top: 15px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 8px 0 8px;
                color: #374151;
                background-color: white;
            }
        """
    
    def get_spinbox_style(self):
        """获取数字输入框样式"""
        return """
            QSpinBox {
                padding: 8px 12px;
                border: 1px solid #d1d5db;
                border-radius: 6px;
                font-size: 14px;
                background-color: white;
            }
            QSpinBox:focus {
                border-color: #2563eb;
            }
            QSpinBox::up-button, QSpinBox::down-button {
                width: 20px;
                border: none;
                background-color: #f8fafc;
            }
            QSpinBox::up-button:hover, QSpinBox::down-button:hover {
                background-color: #e2e8f0;
            }
        """
    
    def get_checkbox_style(self):
        """获取复选框样式"""
        return """
            QCheckBox {
                font-size: 14px;
                spacing: 10px;
                padding: 5px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
            QCheckBox::indicator:unchecked {
                border: 2px solid #d1d5db;
                border-radius: 4px;
                background-color: white;
            }
            QCheckBox::indicator:checked {
                border: 2px solid #2563eb;
                border-radius: 4px;
                background-color: #2563eb;
                image: none;
            }
            QCheckBox::indicator:checked:after {
                content: "✓";
                color: white;
                font-weight: bold;
            }
        """
    
    def load_config_values(self):
        """从配置文件加载值"""
        # 基础配置
        douyin_config = self.config.get('douyin_id', {})
        
        # 批量模式设置
        batch_mode = is_batch_mode_enabled(self.config)
        self.enable_batch_mode.setChecked(batch_mode)
        
        # 根据模式设置ID输入
        if batch_mode:
            # 批量模式：从douyin_ids列表加载
            douyin_ids = get_douyin_ids_from_config(self.config)
            self.batch_id_input.setPlainText('\n'.join(douyin_ids))
            # 如果有单个ID，也显示在单个输入框中（兼容性）
            if douyin_ids:
                self.douyin_id_input.setText(douyin_ids[0])
        else:
            # 单个模式：从douyin_id加载
            single_id = douyin_config.get('douyin_id', '')
            self.douyin_id_input.setText(single_id)
        
        # 更新UI可见性
        self.on_batch_mode_toggled(batch_mode)
        
        scraper_config = self.config.get('scraper', {})
        self.js_timeout_input.setValue(scraper_config.get('js_timeout', 10))
        self.js_retry_input.setValue(scraper_config.get('js_retry', 3))
        
        # 限制配置
        limits_config = self.config.get('limits', {})
        self.max_follower_input.setValue(limits_config.get('max_follower_count', 30))
        self.max_favorite_input.setValue(limits_config.get('max_favorite_count', 20))
        
        # 优化配置 - 固定使用完整优化
        concurrent_config = self.config.get('concurrent', {})
        self.enable_concurrent.setChecked(concurrent_config.get('enable_concurrent_mode', True))
        self.max_concurrent_tabs.setValue(concurrent_config.get('max_concurrent_tabs', 5))
        
        # 批量配置
        batch_config = self.config.get('batch', {})
        self.batch_interval_input.setValue(batch_config.get('batch_interval_seconds', 5))
        self.id_retry_input.setValue(batch_config.get('id_retry_count', 2))
        self.skip_failed_ids.setChecked(batch_config.get('skip_failed_ids', True))
        self.generate_batch_report.setChecked(batch_config.get('generate_batch_report', True))
        
        # 文件命名模式
        pattern = batch_config.get('output_naming_pattern', 'id_timestamp')
        pattern_index = 0
        if pattern == 'timestamp_id':
            pattern_index = 1
        elif pattern == 'id_only':
            pattern_index = 2
        self.naming_pattern.setCurrentIndex(pattern_index)
        
        # 导出配置
        export_config = self.config.get('export', {})
        self.export_follower_json.setChecked(export_config.get('follower_json', True))
        self.export_follower_csv.setChecked(export_config.get('follower_csv', True))
        self.export_favorite_json.setChecked(export_config.get('favorite_json', True))
        self.export_favorite_csv.setChecked(export_config.get('favorite_csv', True))
    
    def on_batch_mode_toggled(self, enabled: bool):
        """批量模式切换事件"""
        # 控制UI元素可见性
        if enabled:
            # 批量模式：显示批量输入，隐藏单个输入
            self.single_id_label.setVisible(False)
            self.douyin_id_input.setVisible(False)
            self.batch_id_label.setVisible(True)
            self.batch_id_input.setVisible(True)
            self.batch_group.setVisible(True)
        else:
            # 单个模式：显示单个输入，隐藏批量输入
            self.single_id_label.setVisible(True)
            self.douyin_id_input.setVisible(True)
            self.batch_id_label.setVisible(False)
            self.batch_id_input.setVisible(False)
            self.batch_group.setVisible(False)
    
    def get_config_dict(self) -> Dict:
        """获取当前配置字典"""
        # 获取抖音ID配置
        batch_mode = self.enable_batch_mode.isChecked()
        
        if batch_mode:
            # 批量模式：从文本框获取ID列表
            batch_text = self.batch_id_input.toPlainText().strip()
            douyin_ids = [id_.strip() for id_ in batch_text.split('\n') if id_.strip()]
            douyin_id_config = {
                'douyin_ids': douyin_ids,
                'enable_batch_mode': True
            }
        else:
            # 单个模式：从单行输入框获取
            single_id = self.douyin_id_input.text().strip()
            douyin_id_config = {
                'douyin_ids': [single_id] if single_id else [],
                'enable_batch_mode': False,
                'douyin_id': single_id  # 保持向后兼容
            }
        
        # 文件命名模式转换
        pattern_map = {
            0: 'id_timestamp',
            1: 'timestamp_id', 
            2: 'id_only'
        }
        
        return {
            'douyin_id': douyin_id_config,
            'scraper': {
                'js_timeout': self.js_timeout_input.value(),
                'js_retry': self.js_retry_input.value(),
                'sleep_between_tries': 0.8,
                'sleep_between_pages': 1.5
            },
            'limits': {
                'max_follower_count': self.max_follower_input.value(),
                'max_favorite_count': self.max_favorite_input.value()
            },
            'optimization': {
                'optimization_stage': 3,  # 固定使用完整优化
                'enable_performance_stats': True,
                'enable_debug_logging': False
            },
            'concurrent': {
                'enable_concurrent_mode': self.enable_concurrent.isChecked(),
                'max_concurrent_tabs': self.max_concurrent_tabs.value()
            },
            'batch': {
                'batch_interval_seconds': self.batch_interval_input.value(),
                'id_retry_count': self.id_retry_input.value(),
                'skip_failed_ids': self.skip_failed_ids.isChecked(),
                'generate_batch_report': self.generate_batch_report.isChecked(),
                'output_naming_pattern': pattern_map.get(self.naming_pattern.currentIndex(), 'id_timestamp')
            },
            'export': {
                'follower_json': self.export_follower_json.isChecked(),
                'follower_csv': self.export_follower_csv.isChecked(),
                'favorite_json': self.export_favorite_json.isChecked(),
                'favorite_csv': self.export_favorite_csv.isChecked()
            }
        }
    
    def save_config(self):
        """保存配置到文件"""
        try:
            config_dict = self.get_config_dict()
            
            # 写入config.toml文件的简化版本
            # 这里简化处理，实际应该使用toml库
            QMessageBox.information(self, "保存成功", "配置已保存！")
        except Exception as e:
            QMessageBox.critical(self, "保存失败", f"配置保存失败：{str(e)}")


class BatchScraperWorker(QThread):
    """批量数据抓取工作线程"""
    
    # 信号定义
    progress_updated = pyqtSignal(int)      # 整体进度更新
    status_updated = pyqtSignal(str)        # 状态更新  
    log_message = pyqtSignal(str, str)      # 日志消息 (message, level)
    batch_progress = pyqtSignal(int, int)   # 批量进度 (current, total)
    current_id_updated = pyqtSignal(str)    # 当前处理ID
    data_received = pyqtSignal(dict)        # 数据接收
    error_occurred = pyqtSignal(str)        # 错误发生
    finished = pyqtSignal()                 # 完成信号
    captcha_required = pyqtSignal(str)      # 滑块验证需求信号 (场景描述)
    captcha_completed = pyqtSignal()        # 验证完成响应信号
    
    def __init__(self, config: Dict, scrape_types: List[str]):
        super().__init__()
        self.config = config
        self.scrape_types = scrape_types
        self.is_running = True
        self.batch_report = BatchProcessingReport()
        
        # 滑块验证等待机制
        from PyQt6.QtCore import QWaitCondition, QMutex
        self.captcha_wait_condition = QWaitCondition()
        self.captcha_mutex = QMutex()
        self.captcha_user_cancelled = False
        
    def wait_for_captcha_completion(self, scene: str) -> bool:
        """等待用户完成滑块验证
        
        Args:
            scene (str): 验证场景描述
            
        Returns:
            bool: True表示用户完成验证，False表示用户取消
        """
        self.log_message.emit(f"检测到滑块验证需求 - 场景: {scene}", "WARNING")
        self.status_updated.emit(f"等待滑块验证: {scene}")
        
        # 发送验证需求信号
        self.captcha_required.emit(scene)
        
        # 等待用户操作
        self.captcha_mutex.lock()
        try:
            # 最多等待5分钟
            timeout_ms = 5 * 60 * 1000  # 5分钟
            if not self.captcha_wait_condition.wait(self.captcha_mutex, timeout_ms):
                # 超时
                self.log_message.emit("滑块验证等待超时", "ERROR")
                return False
            
            # 检查用户是否取消
            if self.captcha_user_cancelled:
                self.log_message.emit("用户取消了滑块验证", "WARNING")
                return False
            
            self.log_message.emit("用户完成滑块验证，继续执行", "SUCCESS")
            return True
            
        finally:
            self.captcha_mutex.unlock()
    
    def on_captcha_completed(self, user_cancelled: bool = False):
        """接收验证完成信号"""
        self.captcha_mutex.lock()
        try:
            self.captcha_user_cancelled = user_cancelled
            self.captcha_wait_condition.wakeAll()
        finally:
            self.captcha_mutex.unlock()
        
    def run(self):
        """执行批量抓取任务"""
        try:
            # 检查是否为批量模式
            if not is_batch_mode_enabled(self.config):
                # 单个模式，调用原有逻辑
                self.run_single_mode()
                return
                
            # 批量模式
            self.run_batch_mode()
            
        except Exception as e:
            self.error_occurred.emit(f"批量抓取失败: {str(e)}")
        finally:
            self.finished.emit()
    
    def run_single_mode(self):
        """单个ID模式运行"""
        try:
            self.status_updated.emit("初始化抓取器...")
            scraper = DouyinScraper(ui_callback=self.wait_for_captcha_completion)
            
            douyin_id = self.config['douyin_id'].get('douyin_id', '')
            if not douyin_id:
                self.error_occurred.emit("请输入抖音ID")
                return
            
            # 获取sec_uid
            self.status_updated.emit("获取用户sec_uid...")
            self.progress_updated.emit(10)
            sec_uid = scraper.fetch_sec_uid(douyin_id)
            
            if not sec_uid:
                self.error_occurred.emit("无法获取用户sec_uid，请检查抖音ID")
                return
            
            self.log_message.emit(f"成功获取sec_uid: {sec_uid}", "SUCCESS")
            
            # 执行抓取任务
            self.execute_scrape_tasks(scraper, sec_uid, douyin_id)
            
        except Exception as e:
            self.error_occurred.emit(f"单个抓取失败: {str(e)}")
    
    def run_batch_mode(self):
        """批量ID模式运行"""
        douyin_ids = get_douyin_ids_from_config(self.config)
        
        if not douyin_ids:
            self.error_occurred.emit("请输入至少一个抖音ID")
            return
        
        total_ids = len(douyin_ids)
        self.log_message.emit(f"开始批量处理，共 {total_ids} 个ID", "INFO")
        
        batch_config = self.config.get('batch', {})
        interval = batch_config.get('batch_interval_seconds', 5)
        
        for i, douyin_id in enumerate(douyin_ids):
            if not self.is_running:
                break
                
            self.current_id_updated.emit(douyin_id)
            self.batch_progress.emit(i + 1, total_ids)
            
            try:
                self.log_message.emit(f"开始处理ID: {douyin_id} ({i+1}/{total_ids})", "INFO")
                
                # 创建临时scraper实例和logger
                temp_scraper = DouyinScraper(ui_callback=self.wait_for_captcha_completion)
                temp_logger = setup_logger()
                
                # 调用main.py中的单个ID处理函数
                result = main.process_single_douyin_id(
                    douyin_id, self.config, temp_scraper, temp_logger,
                    batch_config.get('output_naming_pattern', 'id_timestamp')
                )
                
                if result['success']:
                    self.batch_report.add_success(douyin_id, result.get('processing_time', 0))
                    self.log_message.emit(f"ID {douyin_id} 处理成功", "SUCCESS")
                    
                    # 发送数据到UI进行预览显示
                    if 'data' in result and result['data']:
                        data = result['data']
                        
                        # 发送用户信息
                        if data.get('user_profile'):
                            self.data_received.emit({
                                'type': 'user_profile',
                                'douyin_id': douyin_id,
                                'data': data['user_profile']
                            })
                        
                        # 发送粉丝列表
                        if data.get('followers'):
                            self.data_received.emit({
                                'type': 'followers',
                                'douyin_id': douyin_id,
                                'data': data['followers']
                            })
                        
                        # 发送喜欢列表
                        if data.get('favorites'):
                            self.data_received.emit({
                                'type': 'favorites',
                                'douyin_id': douyin_id,
                                'data': data['favorites']
                            })
                else:
                    error_msg = result.get('error', '未知错误')
                    self.batch_report.add_failure(douyin_id, error_msg)
                    self.log_message.emit(f"ID {douyin_id} 处理失败: {error_msg}", "ERROR")
                
            except Exception as e:
                error_msg = str(e)
                self.batch_report.add_failure(douyin_id, error_msg)
                self.log_message.emit(f"ID {douyin_id} 处理异常: {error_msg}", "ERROR")
            
            # ID间隔等待
            if i < total_ids - 1 and self.is_running:  # 不是最后一个ID才等待
                self.status_updated.emit(f"等待 {interval} 秒后处理下一个ID...")
                for j in range(interval):
                    if not self.is_running:
                        break
                    self.msleep(1000)
        
        # 生成批量报告
        if batch_config.get('generate_batch_report', True):
            try:
                report_file = self.batch_report.save_report()
                self.log_message.emit(f"批量处理报告已保存: {report_file}", "SUCCESS")
            except Exception as e:
                self.log_message.emit(f"保存批量报告失败: {str(e)}", "ERROR")
        
        # 打印统计摘要
        report = self.batch_report.generate_report()
        summary = report['summary']
        
        self.status_updated.emit("批量处理完成")
        self.progress_updated.emit(100)
        self.log_message.emit(
            f"批量处理完成！成功: {summary['successful_ids']}, 失败: {summary['failed_ids']}, 成功率: {summary['success_rate']:.1f}%",
            "SUCCESS"
        )
    
    def execute_scrape_tasks(self, scraper, sec_uid, douyin_id):
        """执行具体的抓取任务"""
        total_tasks = len(self.scrape_types)
        current_task = 0
        
        results = {}
        
        for scrape_type in self.scrape_types:
            if not self.is_running:
                break
            
            current_task += 1
            base_progress = int((current_task - 1) / total_tasks * 80) + 10
            
            if scrape_type == "user_profile":
                self.status_updated.emit("抓取用户信息...")
                self.progress_updated.emit(base_progress)
                user_data = scraper.fetch_user_profile(sec_uid)
                results['user_profile'] = user_data
                self.data_received.emit({'type': 'user_profile', 'data': user_data})
            
            elif scrape_type == "followers":
                max_count = self.config['limits']['max_follower_count']
                self.status_updated.emit(f"抓取粉丝列表 (最多{max_count}条)...")
                self.progress_updated.emit(base_progress)
                
                followers = scraper.fetch_followers(sec_uid, max_count)
                results['followers'] = followers
                self.data_received.emit({'type': 'followers', 'data': followers})
            
            elif scrape_type == "favorites":
                max_count = self.config['limits']['max_favorite_count']
                self.status_updated.emit(f"抓取喜欢列表 (最多{max_count}条)...")
                self.progress_updated.emit(base_progress)
                
                favorites = scraper.fetch_favorites(sec_uid, max_count)
                results['favorites'] = favorites
                self.data_received.emit({'type': 'favorites', 'data': favorites})
        
        self.progress_updated.emit(100)
        self.status_updated.emit("抓取完成")
        self.log_message.emit("所有抓取任务完成！", "SUCCESS")
    
    def stop(self):
        """停止抓取"""
        self.is_running = False
        self.quit()
        self.wait()


class ScraperWorker(QThread):
    """数据抓取工作线程"""
    
    # 信号定义
    progress_updated = pyqtSignal(int)  # 进度更新
    status_updated = pyqtSignal(str)    # 状态更新  
    log_message = pyqtSignal(str)       # 日志消息
    data_received = pyqtSignal(dict)    # 数据接收
    error_occurred = pyqtSignal(str)    # 错误发生
    finished = pyqtSignal()             # 完成信号
    
    def __init__(self, config: Dict, scrape_types: List[str]):
        super().__init__()
        self.config = config
        self.scrape_types = scrape_types
        self.is_running = True
        self.scraper = None
    
    def run(self):
        """执行抓取任务"""
        try:
            self.status_updated.emit("初始化抓取器...")
            self.scraper = DouyinScraper(ui_callback=self.wait_for_captcha_completion)
            
            douyin_id = self.config['douyin_id']['douyin_id']
            if not douyin_id:
                self.error_occurred.emit("请输入抖音ID")
                return
            
            # 获取sec_uid
            self.status_updated.emit("获取用户sec_uid...")
            self.progress_updated.emit(10)
            sec_uid = self.scraper.fetch_sec_uid(douyin_id)
            
            if not sec_uid:
                self.error_occurred.emit("无法获取用户sec_uid，请检查抖音ID")
                return
            
            self.log_message.emit(f"成功获取sec_uid: {sec_uid}")
            
            # 根据选择的类型执行抓取
            total_tasks = len(self.scrape_types)
            current_task = 0
            
            results = {}
            
            for scrape_type in self.scrape_types:
                if not self.is_running:
                    break
                
                current_task += 1
                base_progress = int((current_task - 1) / total_tasks * 80) + 10
                
                if scrape_type == "user_profile":
                    self.status_updated.emit("抓取用户信息...")
                    self.progress_updated.emit(base_progress)
                    user_data = self.scraper.fetch_user_profile(sec_uid)
                    results['user_profile'] = user_data
                    self.data_received.emit({'type': 'user_profile', 'data': user_data})
                
                elif scrape_type == "followers":
                    max_count = self.config['limits']['max_follower_count']
                    self.status_updated.emit(f"抓取粉丝列表 (最多{max_count}条)...")
                    self.progress_updated.emit(base_progress)
                    
                    followers = self.scraper.fetch_followers(sec_uid, max_count)
                    results['followers'] = followers
                    self.data_received.emit({'type': 'followers', 'data': followers})
                
                elif scrape_type == "favorites":
                    max_count = self.config['limits']['max_favorite_count']
                    self.status_updated.emit(f"抓取喜欢列表 (最多{max_count}条)...")
                    self.progress_updated.emit(base_progress)
                    
                    favorites = self.scraper.fetch_favorites(sec_uid, max_count)
                    results['favorites'] = favorites
                    self.data_received.emit({'type': 'favorites', 'data': favorites})
            
            self.progress_updated.emit(100)
            self.status_updated.emit("抓取完成")
            self.log_message.emit("所有抓取任务完成！")
            
        except Exception as e:
            self.error_occurred.emit(f"抓取失败: {str(e)}")
        finally:
            self.finished.emit()
    
    def stop(self):
        """停止抓取"""
        self.is_running = False
        self.quit()
        self.wait()


class ControlPanel(QWidget):
    """功能控制面板"""
    
    # 信号定义
    start_scraping = pyqtSignal(list)  # 开始抓取信号
    stop_scraping = pyqtSignal()       # 停止抓取信号
    
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 标题
        title = QLabel("抓取控制")
        title.setStyleSheet("font-size: 18px; font-weight: bold; color: #1f2937; margin-bottom: 10px;")
        layout.addWidget(title)
        
        # 抓取类型选择组
        self.create_scrape_type_group(layout)
        
        # 控制按钮组
        self.create_control_buttons(layout)
        
        # 添加弹性空间
        layout.addStretch()
    
    def create_scrape_type_group(self, parent_layout):
        """创建抓取类型选择组"""
        group = QGroupBox("抓取类型")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #e5e7eb;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                color: #374151;
            }
        """)
        
        layout = QVBoxLayout(group)
        layout.setSpacing(8)
        
        # 抓取类型复选框
        self.scrape_user_profile = QCheckBox("用户基本信息")
        self.scrape_followers = QCheckBox("粉丝列表")
        self.scrape_favorites = QCheckBox("喜欢列表")
        
        # 默认选中
        self.scrape_user_profile.setChecked(True)
        self.scrape_followers.setChecked(True)
        self.scrape_favorites.setChecked(True)
        
        for checkbox in [self.scrape_user_profile, self.scrape_followers, self.scrape_favorites]:
            checkbox.setStyleSheet("""
                QCheckBox {
                    font-size: 14px;
                    spacing: 8px;
                }
                QCheckBox::indicator {
                    width: 18px;
                    height: 18px;
                }
                QCheckBox::indicator:unchecked {
                    border: 2px solid #d1d5db;
                    border-radius: 4px;
                    background-color: white;
                }
                QCheckBox::indicator:checked {
                    border: 2px solid #2563eb;
                    border-radius: 4px;
                    background-color: #2563eb;
                }
            """)
            layout.addWidget(checkbox)
        
        parent_layout.addWidget(group)
    
    def create_control_buttons(self, parent_layout):
        """创建控制按钮组"""
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)
        
        # 开始按钮
        self.start_btn = ModernButton("开始抓取", "success")
        self.start_btn.clicked.connect(self.on_start_clicked)
        button_layout.addWidget(self.start_btn)
        
        # 停止按钮
        self.stop_btn = ModernButton("停止抓取", "danger")
        self.stop_btn.clicked.connect(self.on_stop_clicked)
        self.stop_btn.setEnabled(False)
        button_layout.addWidget(self.stop_btn)
        
        parent_layout.addLayout(button_layout)
    
    def on_start_clicked(self):
        """开始按钮点击事件"""
        selected_types = []
        
        if self.scrape_user_profile.isChecked():
            selected_types.append("user_profile")
        if self.scrape_followers.isChecked():
            selected_types.append("followers")
        if self.scrape_favorites.isChecked():
            selected_types.append("favorites")
        
        if not selected_types:
            QMessageBox.warning(self, "警告", "请至少选择一种抓取类型！")
            return
        
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        self.start_scraping.emit(selected_types)
    
    def on_stop_clicked(self):
        """停止按钮点击事件"""
        self.stop_scraping.emit()
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
    
    def reset_buttons(self):
        """重置按钮状态"""
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)


class ProgressPanel(QWidget):
    """进度显示面板"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 标题
        title = QLabel("执行状态")
        title.setStyleSheet("font-size: 18px; font-weight: bold; color: #1f2937; margin-bottom: 10px;")
        layout.addWidget(title)
        
        # 状态显示
        self.status_label = QLabel("就绪")
        self.status_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #6b7280;
                padding: 8px 12px;
                background-color: #f9fafb;
                border-radius: 6px;
                border: 1px solid #e5e7eb;
            }
        """)
        layout.addWidget(self.status_label)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 1px solid #d1d5db;
                border-radius: 6px;
                text-align: center;
                font-weight: bold;
                height: 25px;
            }
            QProgressBar::chunk {
                background-color: #10b981;
                border-radius: 5px;
            }
        """)
        layout.addWidget(self.progress_bar)
        
        # 统计信息
        self.create_stats_group(layout)
        
        # 添加弹性空间
        layout.addStretch()
    
    def create_stats_group(self, parent_layout):
        """创建统计信息组"""
        group = QGroupBox("统计信息")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #e5e7eb;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                color: #374151;
            }
        """)
        
        layout = QGridLayout(group)
        layout.setSpacing(10)
        
        # 批量处理进度
        layout.addWidget(QLabel("批量进度:"), 0, 0)
        self.batch_progress_label = QLabel("0/0")
        self.batch_progress_label.setStyleSheet("font-weight: bold; color: #2563eb;")
        layout.addWidget(self.batch_progress_label, 0, 1)
        
        # 当前处理ID
        layout.addWidget(QLabel("当前ID:"), 1, 0)
        self.current_id_label = QLabel("-")
        self.current_id_label.setStyleSheet("font-weight: bold; color: #7c3aed;")
        layout.addWidget(self.current_id_label, 1, 1)
        
        # 统计标签
        layout.addWidget(QLabel("已抓取用户:"), 2, 0)
        self.user_count_label = QLabel("0")
        self.user_count_label.setStyleSheet("font-weight: bold; color: #059669;")
        layout.addWidget(self.user_count_label, 2, 1)
        
        layout.addWidget(QLabel("已抓取粉丝:"), 3, 0)
        self.follower_count_label = QLabel("0")
        self.follower_count_label.setStyleSheet("font-weight: bold; color: #059669;")
        layout.addWidget(self.follower_count_label, 3, 1)
        
        layout.addWidget(QLabel("已抓取视频:"), 4, 0)
        self.video_count_label = QLabel("0")
        self.video_count_label.setStyleSheet("font-weight: bold; color: #059669;")
        layout.addWidget(self.video_count_label, 4, 1)
        
        layout.addWidget(QLabel("开始时间:"), 5, 0)
        self.start_time_label = QLabel("-")
        layout.addWidget(self.start_time_label, 5, 1)
        
        parent_layout.addWidget(group)
    
    def update_batch_progress(self, current: int, total: int):
        """更新批量进度"""
        self.batch_progress_label.setText(f"{current}/{total}")
    
    def update_current_id(self, douyin_id: str):
        """更新当前处理ID"""
        # 截断ID显示，避免太长
        display_id = douyin_id if len(douyin_id) <= 12 else douyin_id[:12] + "..."
        self.current_id_label.setText(display_id)
    
    def update_progress(self, value: int):
        """更新进度"""
        self.progress_bar.setValue(value)
    
    def update_status(self, status: str):
        """更新状态"""
        self.status_label.setText(status)
    
    def update_stats(self, stats: Dict):
        """更新统计信息"""
        if 'user_count' in stats:
            self.user_count_label.setText(str(stats['user_count']))
        if 'follower_count' in stats:
            self.follower_count_label.setText(str(stats['follower_count']))
        if 'video_count' in stats:
            self.video_count_label.setText(str(stats['video_count']))
        if 'start_time' in stats:
            self.start_time_label.setText(stats['start_time'])
    
    def reset_stats(self):
        """重置统计信息"""
        self.batch_progress_label.setText("0/0")
        self.current_id_label.setText("-")
        self.user_count_label.setText("0")
        self.follower_count_label.setText("0")
        self.video_count_label.setText("0")
        self.start_time_label.setText("-")
        self.progress_bar.setValue(0)
        self.status_label.setText("就绪")


class LogPanel(QWidget):
    """日志显示面板"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 标题和控制按钮
        header_layout = QHBoxLayout()
        
        title = QLabel("运行日志")
        title.setStyleSheet("font-size: 16px; font-weight: bold; color: #1f2937;")
        header_layout.addWidget(title)
        
        header_layout.addStretch()
        
        # 清除日志按钮
        clear_btn = QPushButton("清除日志")
        clear_btn.setStyleSheet("""
            QPushButton {
                background-color: #6b7280;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 6px 12px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #4b5563;
            }
        """)
        clear_btn.clicked.connect(self.clear_logs)
        header_layout.addWidget(clear_btn)
        
        layout.addLayout(header_layout)
        
        # 日志文本框
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        
        # 限制最大行数 - 通过QTextDocument设置
        self.log_text.document().setMaximumBlockCount(1000)
        self.log_text.setStyleSheet("""
            QTextEdit {
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 12px;
                background-color: #1f2937;
                color: #f9fafb;
                border: 1px solid #374151;
                border-radius: 6px;
                padding: 10px;
            }
        """)
        layout.addWidget(self.log_text)
    
    def add_log(self, message: str, level: str = "INFO"):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        
        # 根据级别设置颜色
        color = "#f9fafb"  # 默认白色
        if level == "ERROR":
            color = "#ef4444"  # 红色
        elif level == "WARNING":
            color = "#f59e0b"  # 黄色
        elif level == "SUCCESS":
            color = "#10b981"  # 绿色
        
        formatted_message = f'<span style="color: #9ca3af;">[{timestamp}]</span> <span style="color: {color};">[{level}]</span> {message}'
        self.log_text.append(formatted_message)
        
        # 自动滚动到底部
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())
    
    def clear_logs(self):
        """清除所有日志"""
        self.log_text.clear()


class ResultPanel(QWidget):
    """结果展示面板"""
    
    def __init__(self):
        super().__init__()
        self.current_data = {}
        self.init_ui()
    
    def init_ui(self):
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 标题和导出按钮
        header_layout = QHBoxLayout()
        
        title = QLabel("结果预览")
        title.setStyleSheet("font-size: 16px; font-weight: bold; color: #1f2937;")
        header_layout.addWidget(title)
        
        header_layout.addStretch()
        
        # 导出按钮
        self.export_btn = ModernButton("导出数据", "primary")
        self.export_btn.clicked.connect(self.export_data)
        self.export_btn.setEnabled(False)
        header_layout.addWidget(self.export_btn)
        
        layout.addLayout(header_layout)
        
        # 结果选项卡
        self.result_tabs = QTabWidget()
        self.result_tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #d1d5db;
                border-radius: 6px;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #f3f4f6;
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 6px;
                border-top-right-radius: 6px;
            }
            QTabBar::tab:selected {
                background-color: white;
                border-bottom: 2px solid #2563eb;
            }
        """)
        
        # 初始化选项卡
        self.user_table = self.create_result_table()
        self.follower_table = self.create_result_table()
        self.favorite_table = self.create_result_table()
        
        self.result_tabs.addTab(self.user_table, "用户信息")
        self.result_tabs.addTab(self.follower_table, "粉丝列表")
        self.result_tabs.addTab(self.favorite_table, "喜欢列表")
        
        layout.addWidget(self.result_tabs)
    
    def create_result_table(self) -> QTableWidget:
        """创建结果表格"""
        table = QTableWidget()
        table.setAlternatingRowColors(True)
        table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        table.horizontalHeader().setStretchLastSection(True)
        table.setStyleSheet("""
            QTableWidget {
                gridline-color: #e5e7eb;
                background-color: white;
                selection-background-color: #ddd6fe;
            }
            QHeaderView::section {
                background-color: #f3f4f6;
                padding: 8px;
                border: none;
                border-bottom: 1px solid #d1d5db;
                font-weight: bold;
            }
        """)
        return table
    
    def update_data(self, data_info: Dict):
        """更新结果数据"""
        data_type = data_info['type']
        data = data_info['data']
        
        self.current_data[data_type] = data
        
        if data_type == "user_profile":
            self.populate_user_table(data)
        elif data_type == "followers":
            self.populate_follower_table(data)
        elif data_type == "favorites":
            self.populate_favorite_table(data)
        
        self.export_btn.setEnabled(bool(self.current_data))
    
    def populate_user_table(self, user_data: Dict):
        """填充用户信息表格"""
        if not user_data:
            return
        
        self.user_table.setRowCount(1)
        headers = ["昵称", "用户ID", "抖音号", "粉丝数", "关注数", "作品数", "个人简介"]
        self.user_table.setColumnCount(len(headers))
        self.user_table.setHorizontalHeaderLabels(headers)
        
        # 填充数据
        row_data = [
            user_data.get('nickname', ''),
            user_data.get('uid', ''),
            user_data.get('unique_id', ''),
            str(user_data.get('followers', 0)),
            str(user_data.get('following', 0)),
            str(user_data.get('aweme_count', 0)),
            user_data.get('signature', '')
        ]
        
        for col, value in enumerate(row_data):
            item = QTableWidgetItem(str(value))
            self.user_table.setItem(0, col, item)
    
    def populate_follower_table(self, follower_data: List[Dict]):
        """填充粉丝表格"""
        if not follower_data:
            return
        
        self.follower_table.setRowCount(len(follower_data))
        headers = ["昵称", "抖音号", "粉丝数", "关注数", "个人简介"]
        self.follower_table.setColumnCount(len(headers))
        self.follower_table.setHorizontalHeaderLabels(headers)
        
        for row, follower in enumerate(follower_data):
            row_data = [
                follower.get('用户昵称', ''),
                follower.get('用户抖音号', ''),
                str(follower.get('粉丝数', 0)),
                str(follower.get('关注数', 0)),
                follower.get('用户签名', '')
            ]
            
            for col, value in enumerate(row_data):
                item = QTableWidgetItem(str(value))
                self.follower_table.setItem(row, col, item)
    
    def populate_favorite_table(self, favorite_data: List[Dict]):
        """填充喜欢列表表格"""
        if not favorite_data:
            return
        
        self.favorite_table.setRowCount(len(favorite_data))
        headers = ["视频ID", "描述", "作者", "点赞数", "评论数", "分享数", "创建时间"]
        self.favorite_table.setColumnCount(len(headers))
        self.favorite_table.setHorizontalHeaderLabels(headers)
        
        for row, video in enumerate(favorite_data):
            row_data = [
                video.get('视频id', ''),
                video.get('视频描述', ''),
                video.get('作者昵称', ''),
                str(video.get('视频点赞数', 0)),
                str(video.get('视频评论数', 0)),
                str(video.get('视频分享数', 0)),
                video.get('发布时间', '')
            ]
            
            for col, value in enumerate(row_data):
                item = QTableWidgetItem(str(value))
                self.favorite_table.setItem(row, col, item)
    
    def export_data(self):
        """导出数据"""
        if not self.current_data:
            QMessageBox.warning(self, "警告", "没有数据可导出！")
            return
        
        try:
            export_dir = "data"
            if not os.path.exists(export_dir):
                os.makedirs(export_dir)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            exported_files = []
            
            for data_type, data in self.current_data.items():
                if data:
                    # 导出JSON
                    json_filename = f"{data_type}_{timestamp}.json"
                    json_path = os.path.join(export_dir, json_filename)
                    with open(json_path, 'w', encoding='utf-8') as f:
                        json.dump(data, f, ensure_ascii=False, indent=2)
                    exported_files.append(json_path)
                    
                    # 导出CSV (仅对列表数据)
                    if isinstance(data, list) and data:
                        csv_filename = f"{data_type}_{timestamp}.csv"
                        csv_path = os.path.join(export_dir, csv_filename)
                        json_to_csv(data, csv_path)
                        exported_files.append(csv_path)
            
            files_text = "\n".join(exported_files)
            QMessageBox.information(self, "导出成功", f"数据已成功导出到:\n{files_text}")
            
        except Exception as e:
            QMessageBox.critical(self, "导出失败", f"数据导出失败: {str(e)}")


class DouyinScraperMainWindow(QMainWindow):
    """抖音数据抓取器主窗口"""
    
    def __init__(self):
        super().__init__()
        self.scraper_worker = None
        self.start_time = None
        self.init_ui()
        self.setup_connections()
    
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("抖音数据抓取器 v2.0")
        self.setMinimumSize(1200, 800)
        self.resize(1400, 900)
        
        # 应用现代化样式
        self.apply_modern_style()
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局：水平分割
        main_splitter = QSplitter(Qt.Orientation.Horizontal)
        central_widget_layout = QVBoxLayout(central_widget)
        central_widget_layout.addWidget(main_splitter)
        
        # 左侧面板：配置和控制
        left_panel = QWidget()
        left_panel.setFixedWidth(380)
        left_panel.setStyleSheet("""
            QWidget {
                background-color: #f8fafc;
                border-right: 1px solid #e2e8f0;
            }
        """)
        
        left_layout = QVBoxLayout(left_panel)
        left_layout.setContentsMargins(0, 0, 0, 0)
        left_layout.setSpacing(0)
        
        # 配置面板
        self.config_panel = ConfigPanel()
        left_layout.addWidget(self.config_panel, 2)
        
        # 控制面板
        self.control_panel = ControlPanel()
        left_layout.addWidget(self.control_panel, 1)
        
        main_splitter.addWidget(left_panel)
        
        # 右侧面板：结果和日志
        right_panel = QWidget()
        right_splitter = QSplitter(Qt.Orientation.Vertical)
        
        # 上半部分：进度和结果
        top_widget = QWidget()
        top_layout = QHBoxLayout(top_widget)
        top_layout.setContentsMargins(0, 0, 0, 0)
        
        # 进度面板
        self.progress_panel = ProgressPanel()
        self.progress_panel.setFixedWidth(300)
        top_layout.addWidget(self.progress_panel)
        
        # 结果面板
        self.result_panel = ResultPanel()
        top_layout.addWidget(self.result_panel)
        
        right_splitter.addWidget(top_widget)
        
        # 下半部分：日志
        self.log_panel = LogPanel()
        right_splitter.addWidget(self.log_panel)
        
        # 设置分割比例 (3:1)
        right_splitter.setSizes([600, 200])
        
        right_layout = QVBoxLayout(right_panel)
        right_layout.addWidget(right_splitter)
        
        main_splitter.addWidget(right_panel)
        
        # 设置主分割比例
        main_splitter.setSizes([380, 1020])
        
        # 状态栏
        self.statusBar().showMessage("就绪")
    
    def apply_modern_style(self):
        """应用现代化样式"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #ffffff;
            }
            QWidget {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                font-size: 13px;
            }
            QGroupBox {
                margin-top: 12px;
            }
            QSplitter::handle {
                background-color: #e2e8f0;
            }
            QSplitter::handle:horizontal {
                width: 1px;
            }
            QSplitter::handle:vertical {
                height: 1px;
            }
        """)
    
    def setup_connections(self):
        """设置信号连接"""
        # 控制面板信号
        self.control_panel.start_scraping.connect(self.start_scraping)
        self.control_panel.stop_scraping.connect(self.stop_scraping)
    
    def start_scraping(self, scrape_types: List[str]):
        """开始抓取"""
        # 获取配置
        config = self.config_panel.get_config_dict()
        
        # 验证抖音ID
        if is_batch_mode_enabled(config):
            # 批量模式验证
            douyin_ids = get_douyin_ids_from_config(config)
            if not douyin_ids:
                QMessageBox.warning(self, "警告", "请输入至少一个抖音ID！")
                self.control_panel.reset_buttons()
                return
        else:
            # 单个模式验证
            if not config['douyin_id'].get('douyin_id', '').strip():
                QMessageBox.warning(self, "警告", "请输入抖音ID！")
                self.control_panel.reset_buttons()
                return
        
        # 重置统计和日志
        self.progress_panel.reset_stats()
        self.log_panel.add_log("开始初始化抓取任务...", "INFO")
        
        # 记录开始时间
        self.start_time = datetime.now()
        self.progress_panel.update_stats({
            'start_time': self.start_time.strftime("%Y-%m-%d %H:%M:%S")
        })
        
        # 使用新的批量处理工作线程
        self.scraper_worker = BatchScraperWorker(config, scrape_types)
        
        # 连接信号
        self.scraper_worker.progress_updated.connect(self.progress_panel.update_progress)
        self.scraper_worker.status_updated.connect(self.progress_panel.update_status)
        self.scraper_worker.log_message.connect(self.log_panel.add_log)
        self.scraper_worker.data_received.connect(self.result_panel.update_data)
        self.scraper_worker.error_occurred.connect(self.handle_error)
        self.scraper_worker.finished.connect(self.scraping_finished)
        
        # 滑块验证信号
        self.scraper_worker.captcha_required.connect(self.handle_captcha_required)
        
        # 批量处理特有信号
        self.scraper_worker.batch_progress.connect(self.progress_panel.update_batch_progress)
        self.scraper_worker.current_id_updated.connect(self.progress_panel.update_current_id)
        
        # 启动线程
        self.scraper_worker.start()
        
        mode_text = "批量模式" if is_batch_mode_enabled(config) else "单个模式"
        self.log_panel.add_log(f"抓取任务已启动（{mode_text}），类型: {', '.join(scrape_types)}", "SUCCESS")
    
    def stop_scraping(self):
        """停止抓取"""
        if self.scraper_worker and self.scraper_worker.isRunning():
            self.log_panel.add_log("正在停止抓取任务...", "WARNING")
            self.scraper_worker.stop()
            self.progress_panel.update_status("正在停止...")
    
    def handle_error(self, error_msg: str):
        """处理错误"""
        self.log_panel.add_log(f"错误: {error_msg}", "ERROR")
        QMessageBox.critical(self, "抓取错误", error_msg)
        self.control_panel.reset_buttons()
    
    def handle_captcha_required(self, scene: str):
        """处理滑块验证需求"""
        self.log_panel.add_log(f"需要滑块验证 - 场景: {scene}", "WARNING")
        
        # 显示滑块验证对话框
        dialog = CaptchaDialog(scene, self)
        result = dialog.exec()
        
        # 根据用户操作处理结果
        if result == QDialog.DialogCode.Accepted and not dialog.user_cancelled:
            # 用户完成验证
            self.log_panel.add_log("用户确认已完成滑块验证", "SUCCESS")
            self.scraper_worker.on_captcha_completed(user_cancelled=False)
        else:
            # 用户取消任务
            self.log_panel.add_log("用户取消了滑块验证，任务将终止", "WARNING")
            self.scraper_worker.on_captcha_completed(user_cancelled=True)
            
            # 停止抓取任务
            if self.scraper_worker and self.scraper_worker.isRunning():
                self.scraper_worker.stop()
                self.control_panel.reset_buttons()
    
    def scraping_finished(self):
        """抓取完成"""
        self.control_panel.reset_buttons()
        
        if self.start_time:
            elapsed = datetime.now() - self.start_time
            elapsed_str = str(elapsed).split('.')[0]  # 去掉微秒
            self.log_panel.add_log(f"抓取任务完成，总耗时: {elapsed_str}", "SUCCESS")
        
        self.progress_panel.update_status("完成")
        self.statusBar().showMessage("抓取完成")


if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    # 设置应用信息
    app.setApplicationName("抖音数据抓取器")
    app.setApplicationVersion("2.0")
    app.setOrganizationName("DouyinScraper")
    
    # 创建主窗口
    main_window = DouyinScraperMainWindow()
    main_window.show()
    
    sys.exit(app.exec())