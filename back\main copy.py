from __future__ import annotations

from DrissionPage import Chromium, ChromiumOptions
import jmespath
import json
from urllib.parse import urlsplit, parse_qsl, urlencode, urlunsplit
import time
import csv
from typing import List, Dict, Set, Union


def json_to_csv(json_data: Union[str, List[Dict]], csv_filename: str = None) -> str:
    """
    将 JSON 数组数据导出到 CSV 文件，自适应处理所有字段。

    Args:
        json_data: JSON 字符串或 Python 列表，包含字典数组
        csv_filename: CSV 文件名，如果为 None 则自动生成

    Returns:
        str: 生成的 CSV 文件名

    Raises:
        ValueError: 当输入数据格式不正确时
    """
    # 解析 JSON 数据
    if isinstance(json_data, str):
        try:
            data = json.loads(json_data)
        except json.JSONDecodeError as e:
            raise ValueError(f"无效的 JSON 格式: {e}")
    elif isinstance(json_data, list):
        data = json_data
    else:
        raise ValueError("输入数据必须是 JSON 字符串或列表")

    # 检查数据是否为空
    if not data:
        raise ValueError("输入数据为空")

    # 检查是否为字典列表
    if not all(isinstance(item, dict) for item in data):
        raise ValueError("JSON 数组中的所有元素必须是对象(字典)")

    # 自动收集所有可能的字段名
    all_fields = set()
    for item in data:
        all_fields.update(item.keys())

    # 按字母顺序排序字段，确保输出一致性
    fieldnames = sorted(all_fields)

    # 生成文件名
    if csv_filename is None:
        csv_filename = f"export_data_{int(time.time())}.csv"

    # 写入 CSV 文件
    try:
        with open(csv_filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

            # 写入表头
            writer.writeheader()

            # 写入数据行
            for item in data:
                # 确保每行都包含所有字段，缺失的字段用空字符串填充
                row = {field: item.get(field, '') for field in fieldnames}
                writer.writerow(row)

        print(f"CSV 文件已生成: {csv_filename}")
        print(f"包含 {len(data)} 行数据，{len(fieldnames)} 个字段")
        print(f"字段列表: {', '.join(fieldnames)}")

        return csv_filename

    except IOError as e:
        raise IOError(f"写入 CSV 文件失败: {e}")


class DouyinScraper:
    """抖音网页数据抓取器，基于 DrissionPage。"""

    # ---- 全局运行参数（按需修改） ----
    JS_TIMEOUT = 3                 # 每次 run_js 的超时时间（秒）
    JS_RETRY = 2                   # 超时或返回空时的自动重试次数（每个阶段）
    SLEEP_BETWEEN_TRIES = 0.8      # 单页内部重试的间隔（秒）
    SLEEP_BETWEEN_PAGES = 1.5      # 翻页间隔，降低风控概率

    def __init__(self):
        """初始化 Chromium 实例并配置个人数据目录.

        Args:
            user_data_path: 浏览器用户数据路径（新目录，避免历史缓存干扰）
        """
        # options = ChromiumOptions()
        # options.set_user_data_path(user_data_path)
        # self.dp = Chromium(options).latest_tab
        self.dp = Chromium().latest_tab

    # ---------------- 工具方法 ----------------

    @staticmethod
    def _to_json(body) -> dict:
        """将 response.body 统一转换为 dict."""
        if isinstance(body, (bytes, bytearray)):
            return json.loads(body.decode('utf-8', 'ignore'))
        if isinstance(body, str):
            return json.loads(body)
        return body or {}

    @staticmethod
    def _set_query_params(url: str, **kw) -> str:
        """在 URL 上修改或添加查询参数."""
        u = urlsplit(url)
        q = dict(parse_qsl(u.query, keep_blank_values=True))
        for k, v in kw.items():
            if v is not None:
                q[k] = str(v)
        return urlunsplit((u.scheme, u.netloc, u.path, urlencode(q, doseq=True), u.fragment))

    @staticmethod
    def _wait_user_to_solve(scene: str = '操作'):
        """在终端提示用户去浏览器完成滑块/安全验证，完成后回车继续。"""
        print(f"\n[提示] 可能触发了滑块/安全验证（{scene}）。")
        print("        请切换到已打开的浏览器页面完成验证。")
        print("        完成后回到本窗口，按 Enter 继续；输入 q 然后回车可终止。\n")
        ack = input("完成验证后按 Enter 继续（或输入 q 退出）：").strip().lower()
        if ack == 'q':
            raise RuntimeError("用户取消，终止当前任务。")

    def _fetch_json_via_js(self, url: str, scene: str) -> dict | None:
        """统一的 JS 拉取包装：任何 JS 超时/空返回都按触发滑块处理，并能在完成验证后继续原进度。

        流程：
            - 尝试 JS_RETRY 次，run_js 超时时间 JS_TIMEOUT；
            - 仍失败 → 提示用户过滑块；
            - 再尝试 JS_RETRY 次；
            - 若还失败，则抛错。
        """
        js = f"""
return (async () => {{
  const r = await fetch('{url}', {{ credentials: 'include' }});
  try {{ return await r.json(); }} catch (e) {{ return null; }}
}})()
"""

        # 阶段 1：自动重试
        for _ in range(self.JS_RETRY):
            try:
                data = self.dp.run_js(js, timeout=self.JS_TIMEOUT)
            except Exception:
                data = None
            if data:
                return data
            time.sleep(self.SLEEP_BETWEEN_TRIES)

        # 视为滑块
        self._wait_user_to_solve(scene)

        # 阶段 2：完成验证后再次重试
        for _ in range(self.JS_RETRY):
            try:
                data = self.dp.run_js(js, timeout=self.JS_TIMEOUT)
            except Exception:
                data = None
            if data:
                return data
            time.sleep(self.SLEEP_BETWEEN_TRIES)

        raise RuntimeError(f"多次尝试后仍未获取到数据（{scene}）。")

    # ---------------- 业务方法 ----------------

    def fetch_sec_uid(self, douyin_id: str) -> str:
        """通过搜索建议接口获取用户的 sec_uid。"""
        self.dp.listen.clear()
        self.dp.listen.start(f"/aweme/v1/web/api/suggest_words/?query={douyin_id}")
        self.dp.get(f'https://www.douyin.com/search/{douyin_id}')
        pkt = next(self.dp.listen.steps(count=1))
        self.dp.listen.pause(True)

        data = self._to_json(pkt.response.body)
        msg = jmespath.search('data[0].params.extra_info.msg', data)
        payload = json.loads(msg) if msg else {}
        sec_uid = jmespath.search('src_query.sug_sec_uid', payload)
        if not sec_uid:
            raise RuntimeError("未在 suggest_words 响应中找到 sec_uid。")
        return sec_uid

    def fetch_user_profile(self, sec_uid: str) -> dict:
        """获取用户基本信息."""
        self.dp.listen.clear()
        self.dp.listen.start("/aweme/v1/web/user/profile/other/?device_platform=webapp")
        self.dp.get(f'https://www.douyin.com/user/{sec_uid}?from_tab_name=main')
        pkt = next(self.dp.listen.steps(count=1))
        self.dp.listen.pause(True)

        profile = self._to_json(pkt.response.body).get('user', {}) or {}
        return {
            'nickname': profile.get('nickname'),
            'uid': profile.get('uid'),
            'unique_id': profile.get('unique_id'),
            'followers': profile.get('mplatform_followers_count'),
            'following': profile.get('following_count'),
            'signature': profile.get('signature'),
            'aweme_count': profile.get('aweme_count'),
            'favoriting_count': profile.get('favoriting_count'),
        }

    def fetch_followers(self,
                        sec_uid: str,
                        max_items: int = 5000,
                        page_count: int = 20) -> List[Dict]:
        """抓取用户粉丝列表（PC Web），时间游标分页。"""
        self.dp.listen.clear()
        self.dp.listen.start("/aweme/v1/web/user/follower/list/")

        # 进入主页 -> 点击“粉丝”，触发首包
        self.dp.get(f"https://www.douyin.com/user/{sec_uid}?from_tab_name=main")
        locator = 'x://div[@data-e2e="user-info-fans"]'
        self.dp.wait.ele_displayed(locator, timeout=15)
        self.dp.ele(locator).click()

        try:
            # ---- 首包 ----
            first_req_url, first_data = None, None
            for _ in range(self.JS_RETRY):  # 用相同的次数语义以保持一致
                pkt = next(self.dp.listen.steps(count=1))
                first_req_url = pkt.request.url
                first_data = self._to_json(pkt.response.body)
                if (first_data or {}).get('followers'):
                    break
                time.sleep(self.SLEEP_BETWEEN_TRIES)

            if not ((first_data or {}).get('followers')):
                self._wait_user_to_solve('获取粉丝列表（首包）')
                for _ in range(self.JS_RETRY):
                    self.dp.refresh()
                    pkt = next(self.dp.listen.steps(count=1))
                    first_req_url = pkt.request.url
                    first_data = self._to_json(pkt.response.body)
                    if (first_data or {}).get('followers'):
                        break
                    time.sleep(self.SLEEP_BETWEEN_TRIES)

            if not ((first_data or {}).get('followers')):
                raise RuntimeError("多次尝试后仍未获取到粉丝首包数据，请稍后再试。")

            # ---- 收集与去重 ----
            collected: List[Dict] = []
            seen: Set[str] = set()

            def _extend(items: List[Dict]) -> int:
                added = 0
                for it in items or []:
                    k = str(it.get('uid') or it.get('sec_uid') or it.get('unique_id') or id(it))
                    if k in seen:
                        continue
                    seen.add(k)
                    collected.append(it)
                    added += 1
                return added

            # ---- 处理首包 ----
            page_followers = first_data.get('followers') or []
            added = _extend(page_followers)
            has_more = bool(first_data.get('has_more'))
            next_max_time = int(first_data.get('min_time') or 0)
            last_min_time = next_max_time
            stuck_rounds = 0

            print(f"[首包] items={len(page_followers)} (+{added}) "
                  f"has_more={has_more} min_time={first_data.get('min_time')} "
                  f"max_time={first_data.get('max_time')} total={first_data.get('total')}")

            # ---- 翻页循环 ----
            while has_more and len(collected) < max_items:
                query_url = self._set_query_params(
                    first_req_url,
                    max_time=max(0, next_max_time),
                    count=page_count
                )

                page = self._fetch_json_via_js(query_url, scene='获取粉丝列表（翻页）')
                items = (page or {}).get('followers') or []
                page_added = _extend(items)
                has_more = bool((page or {}).get('has_more'))

                print(f"[粉丝翻页] items={len(items)} (+{page_added}) "
                      f"has_more={has_more} min_time={(page or {}).get('min_time')} "
                      f"max_time={(page or {}).get('max_time')} 粉丝数量累积={len(collected)}")
                if not has_more:
                    break

                page_min_time = int((page or {}).get('min_time') or 0)

                # 游标卡住 -> -1 纠偏；连卡两次退出
                if page_min_time >= last_min_time:
                    stuck_rounds += 1
                    next_max_time = max(0, last_min_time - 1)
                    if stuck_rounds >= 2:
                        print("[提示] 时间游标连续未推进，认为已到尽头，提前结束以避免死循环。")
                        break
                else:
                    next_max_time = page_min_time
                    last_min_time = page_min_time
                    stuck_rounds = 0

                time.sleep(self.SLEEP_BETWEEN_PAGES)
            follower_list_json = json.dumps(collected, ensure_ascii=False, indent=2)
            # 处理json数据,只保留特定字段
            follower_list = json.loads(follower_list_json)

            # 定义需要保留的字段（支持嵌套字段）
            keep_fields = {
                '用户UID': 'uid',
                '用户sec_uid': 'sec_uid',
                '用户抖音号': 'unique_id',
                '用户昵称': 'nickname',
                '用户签名': 'signature',
                '用户头像': 'avatar_thumb.url_list[0]',
                '粉丝数': 'mplatform_followers_count',
                '关注数': 'following_count',
                '作品数': 'aweme_count',
                '获赞数': 'favoriting_count',
                '总被赞数': 'total_favorited',
                '是否官方账号': 'is_gov_media_vip',
                '是否为明星': 'is_star',
                '是否认证': 'is_verified',
                '认证类型': 'verification_type',
                '认证信息': 'custom_verify',
                '企业认证原因': 'enterprise_verify_reason'
            }

            def get_nested_value(data, path, default=''):
                """获取嵌套字典中的值，支持点号分隔的路径和数组索引"""
                try:
                    current = data
                    # 处理路径中的数组索引，如 avatar_thumb.url_list[0]
                    import re
                    parts = re.split(r'[\.\[\]]', path)
                    parts = [p for p in parts if p]  # 移除空字符串

                    for part in parts:
                        if part.isdigit():  # 数组索引
                            current = current[int(part)]
                        else:  # 字典键
                            current = current[part]
                    return current if current is not None else default
                except (KeyError, TypeError, AttributeError, IndexError):
                    return default

            # 只保留指定字段，创建新的清理后的列表
            cleaned_followers = []
            for follower in follower_list:
                cleaned_follower = {}
                for new_field, path in keep_fields.items():
                    cleaned_follower[new_field] = get_nested_value(follower, path)
                cleaned_followers.append(cleaned_follower)
            

            return cleaned_followers[:max_items]

        finally:
            self.dp.listen.pause(True)

    def fetch_video_info(self,
                        video_id: str) -> List[Dict]:
        """抓取视频信息。"""
        self.dp.listen.clear()
        self.dp.listen.start("/aweme/v1/web/aweme/detail/?device_platform=webapp")
        video_url = f"https://www.douyin.com/video/{video_id}"

        # 打开点赞页 → 首包
        self.dp.get(video_url)

        first_req, data = None, None
        for _ in range(self.JS_RETRY):
                pkt = next(self.dp.listen.steps(count=1))
                data = self._to_json(pkt.response.body)
                if (data or {}).get('aweme_detail'):
                    break
                time.sleep(self.SLEEP_BETWEEN_TRIES)

        if not ((data or {}).get('aweme_detail')):
                self._wait_user_to_solve('获取视频信息（首包）')
                for _ in range(self.JS_RETRY):
                    self.dp.refresh()
                    pkt = next(self.dp.listen.steps(count=1))
                    data = self._to_json(pkt.response.body)
                    if (data or {}).get('aweme_detail'):
                        break
                    time.sleep(self.SLEEP_BETWEEN_TRIES)

        items = (data or {}).get('aweme_detail') or []
        if not items:
                raise RuntimeError("多次尝试后仍未获取到视频包数据，请稍后再试。")
        #保留特定字段
        keep_fields = {
            '视频id': 'aweme_id', #视频id
            '视频标题': 'text', #视频标题
            '视频描述': 'desc', #视频描述
            '视频作者id': 'author.uid', #视频作者id
            '视频作者昵称': 'author.nickname', #视频作者昵称
            '视频作者抖音号': 'author.unique_id', #视频作者抖音号
            '视频作者头像': 'author.avatar_larger.url_list[0]', #视频作者头像
            '视频作者签名': 'author.signature', #视频作者签名
            '视频作者粉丝数': 'author.mplatform_followers_count', #视频作者粉丝数
            '视频作者关注数': 'author.following_count', #视频作者关注数
            '视频作者作品数': 'author.aweme_count', #视频作者作品数
            '视频作者获赞数': 'author.favoriting_count', #视频作者获赞数
            '视频作者总被赞数': 'author.total_favorited', #视频作者总被赞数
            '视频作者是否官方账号': 'author.is_gov_media_vip', #视频作者是否官方账号
            '视频作者是否为明星': 'author.is_star', #视频作者是否为明星
            '视频作者是否认证': 'author.is_verified', #视频作者是否认证
            '视频作者认证类型': 'author.verification_type', #视频作者认证类型
            '视频作者认证信息': 'author.custom_verify', #视频作者认证信息
            '视频作者企业认证原因': 'author.enterprise_verify_reason', #视频作者企业认证原因
        }
        def get_nested_value(data, path, default=''):
            """获取嵌套字典中的值，支持点号分隔的路径和数组索引"""
            try:
                current = data
                import re
                parts = re.split(r'[\.\[\]]', path)
                parts = [p for p in parts if p]  # 移除空字符串

                for part in parts:
                    if part.isdigit():  # 数组索引
                        current = current[int(part)]
                    else:  # 字典键
                        current = current[part]
                return current if current is not None else default
            except (KeyError, TypeError, AttributeError, IndexError):
                return default
        # 只保留指定字段，创建新的清理后的列表
        cleaned_videos = []
        for item in items:
            cleaned_video = {}
            for new_field, path in keep_fields.items():
                cleaned_video[new_field] = get_nested_value(item, path)
            cleaned_videos.append(cleaned_video)
            return cleaned_videos



    def fetch_favorites(self, sec_uid: str, max_items: int = 200) -> List[Dict]:
        """获取用户点赞的视频列表（演示），同样按“任何 JS 超时 → 滑块处理”。"""
        self.dp.listen.clear()
        self.dp.listen.start("/aweme/v1/web/aweme/favorite/?device_platform=webapp")
        like_url = f"https://www.douyin.com/user/{sec_uid}?from_tab_name=main&showTab=like"

        try:
            # 打开点赞页 → 首包
            self.dp.get(like_url)

            first_req, data = None, None
            for _ in range(self.JS_RETRY):
                pkt = next(self.dp.listen.steps(count=1))
                first_req = pkt.request.url
                data = self._to_json(pkt.response.body)
                if (data or {}).get('aweme_list'):
                    break
                time.sleep(self.SLEEP_BETWEEN_TRIES)

            if not ((data or {}).get('aweme_list')):
                self._wait_user_to_solve('获取喜欢列表（首包）')
                for _ in range(self.JS_RETRY):
                    self.dp.refresh()
                    pkt = next(self.dp.listen.steps(count=1))
                    first_req = pkt.request.url
                    data = self._to_json(pkt.response.body)
                    if (data or {}).get('aweme_list'):
                        break
                    time.sleep(self.SLEEP_BETWEEN_TRIES)

            items = (data or {}).get('aweme_list') or []
            if not items:
                raise RuntimeError("多次尝试后仍未获取到喜欢首包数据，请稍后再试。")

            has_more = data.get('has_more', 0)
            cursor = data.get('max_cursor', 0)
            print(f"[首包-喜欢] items={len(items)} has_more={has_more} "
                  f"min_cursor={data.get('min_cursor')} max_cursor={data.get('max_cursor')}")

            # 翻页
            while has_more and len(items) < max_items:
                next_url = self._set_query_params(first_req, max_cursor=cursor, min_cursor=0)
                page_data = self._fetch_json_via_js(next_url, scene='获取喜欢列表（翻页）')

                page_items = (page_data or {}).get('aweme_list') or []
                items.extend(page_items)
                has_more = (page_data or {}).get('has_more', 0)
                cursor = (page_data or {}).get('max_cursor', 0)

                print(f"[喜欢翻页] items={len(page_items)} has_more={has_more} "
                      f"min_cursor={(page_data or {}).get('min_cursor')} "
                      f"max_cursor={(page_data or {}).get('max_cursor')} 累积={len(items)}")

                time.sleep(self.SLEEP_BETWEEN_PAGES)

            
            # 定义需要保留的字段（支持嵌套字段）
            video_id_fields = {
                '视频id': 'aweme_id', #视频id
            }

            video_id_list = []
            for item in items:
                video_id_list.append(get_nested_value(item, video_id_fields['视频id']))
                video_info = self.fetch_video_info(video_id_fields['视频id'])
                video_id_list.append(video_info)
            
            # 处理视频数据，只保留特定字段
            keep_fields = {
                '视频id': 'aweme_id', #视频id
                '视频标题': 'text', #视频标题
                '视频描述': 'desc', #视频描述
            }



            def get_nested_value(data, path, default=''):
                """获取嵌套字典中的值，支持点号分隔的路径"""
                keys = path.split('.')
                current = data
                try:
                    for key in keys:
                        if isinstance(current, dict) and key in current:
                            current = current[key]
                        else:
                            return default
                    return current if current is not None else default
                except (KeyError, TypeError, AttributeError):
                    return default

            # 只保留指定字段，创建新的清理后的列表
            cleaned_favorites = []
            for item in items:
                cleaned_item = {}
                for new_field, path in keep_fields.items():
                    cleaned_item[new_field] = get_nested_value(item, path)
                cleaned_favorites.append(cleaned_item)

            return cleaned_favorites[:max_items]

        finally:
            self.dp.listen.pause(True)



    def close(self):
        """退出并关闭浏览器."""
        self.dp.browser.quit()


def main():
    """主函数：演示抓取粉丝列表与喜欢列表。"""
    # scraper = DouyinScraper(user_data_path=r'D:\temp\dp_profile_clean')
    Max_follower_count = 30
    Max_favorite_count = 30
    #是否导出粉丝信息--json
    is_export_follower_json = True
    #是否导出粉丝信息--csv
    is_export_follower_csv = True
    #是否导出喜欢视频信息--json
    is_export_favorite_json = True
    #是否导出喜欢视频信息--csv
    is_export_favorite_csv = True

    scraper = DouyinScraper()
    try:
        douyin_id = "96967475948"
        sec_uid = scraper.fetch_sec_uid(douyin_id)
        print(f"sec_uid: {sec_uid}\n{'='*60}")

        profile = scraper.fetch_user_profile(sec_uid)
        for k, v in profile.items():
            print(f"{k}: {v}")
        print('=' * 60)

        # 粉丝列表（时间游标分页）
        followers = scraper.fetch_followers(
            sec_uid=sec_uid,
            max_items=Max_follower_count,     # 按需调整
            page_count=20
        )
        print(f"\n粉丝抓取完成：{len(followers)} 条。")
        if is_export_follower_json:
            # 保存粉丝详情到 JSON 文件
            json_filename = f"followers_details_{int(time.time())}.json"
            with open(json_filename, 'w', encoding='utf-8') as f:
                json.dump(followers, f, ensure_ascii=False, indent=2)
            print(f"粉丝详情已保存到 JSON 文件: {json_filename} (共{len(followers)}条记录)")

        if is_export_follower_csv:
            # 导出粉丝详情到 CSV 文件
            csv_filename = f"followers_details_{int(time.time())}.csv"
            json_to_csv(followers, csv_filename)




        # 喜欢列表（可选）
        favorites = scraper.fetch_favorites(
            sec_uid=sec_uid,
            max_items=Max_favorite_count
        )
        print(f"喜欢抓取完成：{len(favorites)} 条。")

        if is_export_favorite_json:
            # 保存喜欢列表到 JSON 文件
            favorites_json_filename = f"favorites_details_{int(time.time())}.json"
            with open(favorites_json_filename, 'w', encoding='utf-8') as f:
                json.dump(favorites, f, ensure_ascii=False, indent=2)
            print(f"喜欢详情已保存到 JSON 文件: {favorites_json_filename} (共{len(favorites)}条记录)")
        if is_export_favorite_csv:
            # 导出喜欢列表到 CSV 文件
            favorites_csv_filename = f"favorites_details_{int(time.time())}.csv"
            json_to_csv(favorites, favorites_csv_filename)

    finally:
        # scraper.close()
        print(f"任务完成")


if __name__ == "__main__":
    main()
