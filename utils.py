"""
工具函数模块
包含数据处理和转换相关的通用函数
"""

import json
import csv
import time
import re
import os
from typing import List, Dict, Union
from datetime import datetime


def json_to_csv(json_data: Union[str, List[Dict]], csv_filename: str = None) -> str:
    """
    将 JSON 数组数据导出到 CSV 文件，自适应处理所有字段。

    Args:
        json_data: JSON 字符串或 Python 列表，包含字典数组
        csv_filename: CSV 文件名，如果为 None 则自动生成

    Returns:
        str: 生成的 CSV 文件名

    Raises:
        ValueError: 当输入数据格式不正确时
    """
    # 解析 JSON 数据
    if isinstance(json_data, str):
        try:
            data = json.loads(json_data)
        except json.JSONDecodeError as e:
            raise ValueError(f"无效的 JSON 格式: {e}")
    elif isinstance(json_data, list):
        data = json_data
    else:
        raise ValueError("输入数据必须是 JSON 字符串或列表")

    # 检查数据是否为空
    if not data:
        raise ValueError("输入数据为空")

    # 检查是否为字典列表
    if not all(isinstance(item, dict) for item in data):
        raise ValueError("JSON 数组中的所有元素必须是对象(字典)")

    # 自动收集所有可能的字段名
    all_fields = set()
    for item in data:
        all_fields.update(item.keys())

    # 按字母顺序排序字段，确保输出一致性
    fieldnames = sorted(all_fields)

    # 生成文件名
    if csv_filename is None:
        csv_filename = get_data_file_path(f"export_data_{int(time.time())}.csv")

    # 写入 CSV 文件
    try:
        with open(csv_filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

            # 写入表头
            writer.writeheader()

            # 写入数据行
            for item in data:
                # 确保每行都包含所有字段，缺失的字段用空字符串填充
                row = {field: item.get(field, '') for field in fieldnames}
                writer.writerow(row)

        print(f"CSV 文件已生成: {csv_filename}")
        print(f"包含 {len(data)} 行数据，{len(fieldnames)} 个字段")
        print(f"字段列表: {', '.join(fieldnames)}")

        return csv_filename

    except IOError as e:
        raise IOError(f"写入 CSV 文件失败: {e}")


def get_nested_value(data, path, default=''):
    """
    获取嵌套字典中的值，支持点号分隔的路径和数组索引。
    
    Args:
        data: 要查询的数据字典
        path: 路径字符串，如 'user.avatar.url_list[0]'
        default: 默认值，当路径不存在时返回
        
    Returns:
        查询到的值或默认值
        
    Examples:
        >>> data = {'user': {'name': 'test', 'tags': ['a', 'b']}}
        >>> get_nested_value(data, 'user.name')
        'test'
        >>> get_nested_value(data, 'user.tags[0]')
        'a'
    """
    if not path:
        return default
        
    try:
        current = data
        # 处理路径中的数组索引，如 avatar_thumb.url_list[0]
        parts = re.split(r'[\.\[\]]', path)
        parts = [p for p in parts if p]  # 移除空字符串
        
        for part in parts:
            if part.isdigit():
                # 数组索引
                current = current[int(part)]
            else:
                # 字典键
                current = current[part]
                
        return current if current is not None else default
        
    except (KeyError, IndexError, TypeError, AttributeError):
        return default


def ensure_directory(directory: str) -> str:
    """
    确保目录存在，如果不存在则创建。

    Args:
        directory (str): 目录路径

    Returns:
        str: 目录的绝对路径
    """
    if not os.path.exists(directory):
        os.makedirs(directory, exist_ok=True)
        print(f"📁 创建目录: {directory}")
    return os.path.abspath(directory)


def get_data_file_path(filename: str) -> str:
    """
    获取数据文件的完整路径（在 data 目录下）。

    Args:
        filename (str): 文件名

    Returns:
        str: 完整的文件路径
    """
    data_dir = ensure_directory("data")
    return os.path.join(data_dir, filename)


def get_log_file_path(filename: str) -> str:
    """
    获取日志文件的完整路径（在 logs 目录下）。

    Args:
        filename (str): 文件名

    Returns:
        str: 完整的文件路径
    """
    log_dir = ensure_directory("logs")
    return os.path.join(log_dir, filename)


def load_config():
    """
    加载TOML配置，保持向后兼容。

    Returns:
        dict: 配置字典，如果配置文件不存在或解析失败则返回默认配置
    """
    try:
        import tomllib
        with open('config.toml', 'rb') as f:
            return tomllib.load(f)
    except (ImportError, FileNotFoundError):
        # 如果没有tomllib或配置文件，返回默认配置
        return {
            'scraper': {
                'js_timeout': 10,
                'js_retry': 3,
                'sleep_between_tries': 0.8,
                'sleep_between_pages': 1.5,
                'video_retry_base_delay': 0.8,
                'video_retry_extra_delay_min': 0.5,
                'video_retry_extra_delay_max': 0.8
            },
            'limits': {
                'max_follower_count': 30,
                'max_favorite_count': 30
            },
            'data_quality': {
                'listener_cleanup_timeout': 1.0,
                'listener_cleanup_rounds': 3,
                'strict_data_validation': True,
                'enable_deduplication': True
            },
            'export': {
                'follower_json': True,
                'follower_csv': True,
                'favorite_json': True,
                'favorite_csv': True
            },
            'douyin_id': {
                'douyin_ids': ["96967475948"],
                'enable_batch_mode': False
            },
            'batch': {
                'batch_interval_seconds': 5,
                'id_retry_count': 2,
                'skip_failed_ids': True,
                'generate_batch_report': True,
                'batch_report_filename': "",
                'output_naming_pattern': "id_timestamp"
            }
        }


def get_douyin_ids_from_config(config: dict) -> List[str]:
    """
    从配置中获取抖音ID列表，兼容新旧配置格式。
    
    Args:
        config (dict): 配置字典
        
    Returns:
        List[str]: 抖音ID列表
    """
    douyin_config = config.get('douyin_id', {})
    
    # 新格式：douyin_ids列表
    if 'douyin_ids' in douyin_config:
        ids = douyin_config['douyin_ids']
        if isinstance(ids, list):
            return [str(id_) for id_ in ids if id_]
        else:
            return [str(ids)] if ids else []
    
    # 旧格式：单个douyin_id，保持向后兼容
    if 'douyin_id' in douyin_config:
        single_id = douyin_config['douyin_id']
        return [str(single_id)] if single_id else []
    
    return []


def is_batch_mode_enabled(config: dict) -> bool:
    """
    检查是否启用批量处理模式。
    
    Args:
        config (dict): 配置字典
        
    Returns:
        bool: True表示启用批量模式，False表示兼容模式（仅处理第一个ID）
    """
    douyin_config = config.get('douyin_id', {})
    return douyin_config.get('enable_batch_mode', False)


def generate_batch_filename(douyin_id: str, file_type: str, pattern: str = "id_timestamp") -> str:
    """
    为批量处理生成带ID标识的文件名。
    
    Args:
        douyin_id (str): 抖音ID
        file_type (str): 文件类型（如 "followers_details", "favorites_details"）
        pattern (str): 文件名模式
        
    Returns:
        str: 生成的文件名
    """
    timestamp = int(time.time())
    timestamp_str = datetime.fromtimestamp(timestamp).strftime('%Y%m%d_%H%M%S')
    
    if pattern == "id_only":
        return f"{file_type}_{douyin_id}"
    elif pattern == "timestamp_id":
        return f"{file_type}_{timestamp_str}_{douyin_id}"
    else:  # "id_timestamp" 默认模式
        return f"{file_type}_{douyin_id}_{timestamp}"


class BatchProcessingReport:
    """批量处理统计报告类"""
    
    def __init__(self):
        self.start_time = time.time()
        self.processed_ids = []
        self.successful_ids = []
        self.failed_ids = []
        self.errors = {}
        self.processing_times = {}
        
    def add_success(self, douyin_id: str, processing_time: float, details: dict = None):
        """记录成功处理的ID"""
        self.successful_ids.append(douyin_id)
        self.processed_ids.append(douyin_id)
        self.processing_times[douyin_id] = processing_time
        
    def add_failure(self, douyin_id: str, error: str, processing_time: float = 0):
        """记录失败处理的ID"""
        self.failed_ids.append(douyin_id)
        self.processed_ids.append(douyin_id)
        self.errors[douyin_id] = error
        self.processing_times[douyin_id] = processing_time
        
    def generate_report(self) -> dict:
        """生成统计报告"""
        total_time = time.time() - self.start_time
        
        report = {
            'summary': {
                'total_ids': len(self.processed_ids),
                'successful_ids': len(self.successful_ids),
                'failed_ids': len(self.failed_ids),
                'success_rate': len(self.successful_ids) / len(self.processed_ids) * 100 if self.processed_ids else 0,
                'total_processing_time': total_time,
                'average_time_per_id': sum(self.processing_times.values()) / len(self.processing_times) if self.processing_times else 0
            },
            'successful_ids': self.successful_ids,
            'failed_ids': self.failed_ids,
            'errors': self.errors,
            'processing_times': self.processing_times,
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        
        return report
        
    def save_report(self, filename: str = None) -> str:
        """保存报告到文件"""
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = get_data_file_path(f"batch_report_{timestamp}.json")
        
        report = self.generate_report()
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
            
        return filename
        
    def print_summary(self):
        """打印处理结果摘要"""
        report = self.generate_report()
        summary = report['summary']
        
        print("\n" + "="*60)
        print("批量处理结果统计")
        print("="*60)
        print(f"总处理ID数量: {summary['total_ids']}")
        print(f"成功处理: {summary['successful_ids']}")
        print(f"失败处理: {summary['failed_ids']}")
        print(f"成功率: {summary['success_rate']:.1f}%")
        print(f"总耗时: {summary['total_processing_time']:.1f}秒")
        if summary['average_time_per_id'] > 0:
            print(f"平均每ID耗时: {summary['average_time_per_id']:.1f}秒")
        
        if self.failed_ids:
            print(f"\n失败的ID列表:")
            for failed_id in self.failed_ids:
                error = self.errors.get(failed_id, "未知错误")
                print(f"   - {failed_id}: {error}")
        
        print("="*60)
