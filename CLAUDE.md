# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

基于 DrissionPage 4.x 的高性能抖音数据抓取器，采用模块化架构和多阶段性能优化策略，支持获取用户基本信息、粉丝列表和点赞视频等数据。现已支持批量处理、并发抓取和图形界面操作。

## 常用开发命令

```bash
# 主要运行方式
python launch_ui.py         # 图形界面启动器（推荐）
python main.py              # 命令行版本
python ui_main.py           # 直接启动图形界面

# 开发和测试
pip install DrissionPage jmespath PyQt6    # 安装依赖
python test_ui.py           # UI功能测试
python data_validator.py   # 数据验证
python -c "from douyin import DouyinScraper; scraper = DouyinScraper(); print('模块导入成功')"  # 模块测试

# 配置和语法检查
python -m py_compile douyin.py     # 语法检查
python -c "from utils import load_config; print(load_config())"  # 配置文件测试
```

## 项目架构

### 核心模块架构
- **douyin.py**: DouyinScraper 核心业务逻辑，包含所有抓取功能和并发处理
- **main.py**: 命令行版本程序入口，支持单个和批量处理模式
- **ui_main.py**: Qt6图形界面主文件，包含配置面板、控制面板、进度面板、结果面板和日志面板
- **launch_ui.py**: UI启动器，自动检查依赖并启动图形界面
- **utils.py**: 工具函数、配置管理、批量处理工具和数据转换
- **logger.py**: 统一日志系统模块
- **config.toml**: 外置配置文件，支持批量处理和并发处理配置

### UI架构组件详解
- **ConfigPanel**: 配置设置面板，支持单个/批量模式切换，包含滚动区域和6个配置组
- **ControlPanel**: 功能控制面板，抓取类型选择和执行控制
- **ProgressPanel**: 进度显示面板，批量进度和统计信息，固定300px宽度
- **ResultPanel**: 结果展示面板，多标签页表格展示（用户信息/粉丝/喜欢），支持数据导出
- **LogPanel**: 日志显示面板，彩色分级日志（INFO/SUCCESS/WARNING/ERROR），自动滚动
- **CaptchaDialog**: 滑块验证提示对话框，现代化UI设计，替代控制台交互
- **BatchScraperWorker**: 批量抓取工作线程，支持滑块验证信号和并发处理

### 线程通信架构
```python
# 关键信号定义
class BatchScraperWorker(QThread):
    progress_updated = pyqtSignal(int)      # 整体进度更新
    status_updated = pyqtSignal(str)        # 状态更新  
    log_message = pyqtSignal(str, str)      # 日志消息 (message, level)
    batch_progress = pyqtSignal(int, int)   # 批量进度 (current, total)
    data_received = pyqtSignal(dict)        # 数据接收
    captcha_required = pyqtSignal(str)      # 滑块验证需求信号
    captcha_completed = pyqtSignal()        # 验证完成响应信号
```

### 关键设计特点
- **双模式支持**: 图形界面（推荐）+ 命令行版本
- **批量处理架构**: 支持多个抖音ID的顺序处理，可配置间隔和重试
- **配置驱动**: 所有参数通过 config.toml 管理，支持无配置文件运行
- **三阶段性能优化**: 监听器复用 → 队列清理 → 加载模式优化
- **并发处理**: 支持多标签页同时获取视频信息（max_concurrent_tabs: 2-5）
- **滑块验证UI**: 现代化验证提示对话框，支持跨线程同步等待机制
- **标签页池管理**: 自动创建、分配、归还和清理标签页资源
- **自动目录管理**: 数据保存到 data/，日志保存到 logs/

### 滑块验证UI系统
```python
# DouyinScraper 初始化支持UI回调
def __init__(self, ui_callback=None):
    self.ui_callback = ui_callback  # UI模式回调函数

# UI模式滑块验证处理
def _wait_user_to_solve(self, scene: str = '操作'):
    if self.ui_callback:
        success = self.ui_callback(scene)  # 调用UI对话框
        if not success:
            raise RuntimeError("用户取消，终止当前任务。")
    # 命令行模式处理...

# 线程同步机制 (ui_main.py)
from PyQt6.QtCore import QWaitCondition, QMutex
self.captcha_wait_condition = QWaitCondition()
self.captcha_mutex = QMutex()
```

### 并发标签页池架构
```python
# ConcurrentVideoFetcher 类 (douyin.py)
class ConcurrentVideoFetcher:
    def __init__(self, scraper_instance, max_tabs: int):
        self.tabs = []                    # 标签页池
        self.available_tabs = queue.Queue()  # 可用标签页队列
        self.results_lock = threading.Lock() # 线程安全锁
    
    def cleanup(self):
        """清理标签页池并关闭相关标签页"""
        for tab in self.tabs:
            tab.listen.stop()  # 停止监听器
            tab.close()        # 关闭标签页
        self.tabs.clear()      # 清空列表
```

## 核心配置 (config.toml)

批量处理和并发配置示例：
```toml
[douyin_id]
# 支持批量处理多个抖音ID
douyin_ids = [
    "96967475948",
    "testid123",
    # "另一个抖音ID"
]
enable_batch_mode = true

[optimization]  
optimization_stage = 3          # 性能优化阶段（1-3，默认3）
enable_performance_stats = true

[concurrent]
enable_concurrent_mode = true   # 并发模式开关
max_concurrent_tabs = 5         # 最大并发标签页数（建议2-4）
tab_init_delay = 2.0           # 标签页初始化间隔
concurrent_debug_logging = false

[batch]
batch_interval_seconds = 5      # ID间隔时间
id_retry_count = 2             # 单个ID重试次数
skip_failed_ids = true         # 跳过失败ID继续处理
generate_batch_report = true   # 生成批量处理报告
output_naming_pattern = "id_timestamp"  # 文件命名模式

[limits]
max_follower_count = 30         # 最大粉丝抓取数量
max_favorite_count = 20         # 最大点赞视频抓取数量
```

## DrissionPage 4.x 关键实现

### 批量处理架构
```python
# main.py 中的批量处理主函数
def process_batch_douyin_ids(douyin_ids: List[str], config: dict):
    for douyin_id in douyin_ids:
        result = process_single_douyin_id(douyin_id, config, scraper, logger)
        # 处理结果和错误恢复逻辑
```

### 并发处理机制
```python
# 并发模式自动选择 (douyin.py)
def fetch_favorites(self, sec_uid: str, max_items: int = 200):
    if self.ENABLE_CONCURRENT_MODE and self.MAX_CONCURRENT_TABS > 1:
        return self.fetch_favorites_concurrent(sec_uid, max_items)
    # 串行模式处理
```

### 核心优化策略
```python
# 监听器复用模式 - 避免频繁重置
self.dp.listen.start("/aweme/v1/web/aweme/detail/?device_platform=webapp")
for video_id in video_list:
    packet = self.dp.listen.wait(timeout=self.JS_TIMEOUT, raise_err=True)
```

### DouyinScraper 核心方法
- `fetch_sec_uid(douyin_id)`: 通过抖音ID获取sec_uid
- `fetch_user_profile(sec_uid)`: 获取用户基本信息  
- `fetch_followers(sec_uid, max_items)`: 抓取粉丝列表
- `fetch_favorites(sec_uid, max_items)`: 获取点赞视频列表（自动选择串行/并发）
- `fetch_favorites_concurrent(sec_uid, max_items)`: 并发版本的视频抓取
- `fetch_video_info(video_id)`: 获取单个视频详细信息

### 数据字段映射 (重要)
```python
# douyin.py 输出字段 (中文) → ui_main.py 显示字段
粉丝数据字段映射：
- '用户昵称' → 表格显示昵称列
- '用户抖音号' → 表格显示抖音号列  
- '粉丝数' → 表格显示粉丝数列
- '关注数' → 表格显示关注数列
- '用户签名' → 表格显示个人简介列

视频数据字段映射：
- '视频id' → 表格显示视频ID列
- '视频描述' → 表格显示描述列
- '作者昵称' → 表格显示作者列
- '视频点赞数' → 表格显示点赞数列
- '视频评论数' → 表格显示评论数列
- '视频分享数' → 表格显示分享数列
- '发布时间' → 表格显示创建时间列
```

### 批量处理工具函数 (utils.py)
- `load_config()`: 加载配置文件，支持默认值
- `get_douyin_ids_from_config(config)`: 从配置获取抖音ID列表，兼容新旧格式
- `is_batch_mode_enabled(config)`: 检查是否启用批量模式
- `generate_batch_filename(douyin_id, file_type, pattern)`: 生成批量文件名
- `BatchProcessingReport`: 批量处理统计报告类
- `json_to_csv()`: JSON数据转CSV文件

## 开发注意事项

### 性能优化核心
1. **监听器复用**: 避免重复设置监听器，显著提升性能（60-70%提升）
2. **队列清理机制**: 防止数据包混乱，提高数据准确性  
3. **并发处理**: 多标签页同时获取视频信息，注意风控风险
4. **加载模式优化**: 完整优化包含所有性能优化策略
5. **配置优先**: 优先通过 config.toml 修改参数，而非直接修改代码

### 批量处理策略
- **错误恢复**: 支持单个ID失败时继续处理其他ID
- **重试机制**: 可配置的ID级别重试次数
- **文件命名**: 支持多种命名模式避免文件冲突
- **统计报告**: 自动生成批量处理统计报告

### 并发处理注意事项
- **标签页清理**: 并发处理完成后自动清理标签页池
- **风控控制**: 建议并发数量2-4个，避免触发反爬机制
- **资源管理**: 使用 ConcurrentVideoFetcher 类管理标签页池
- **错误隔离**: 单个标签页失败不影响其他并发任务

### 代码维护原则
- **单文件不超过 500 行**，接近时应重构拆分
- **按职责清晰组织模块**（功能/责任分组）
- **为每个函数编写 Google 风格文档字符串**
- **优先编辑现有文件，避免创建新文件**

### UI开发指南
- **线程安全**: UI更新使用工作线程和信号槽机制
- **实时反馈**: 进度更新、状态显示、日志记录实时更新
- **用户体验**: 支持批量模式切换、结果预览、数据导出

### 风控处理策略
- 内置防风控间隔设置（sleep_between_pages: 1.5s）
- 支持滑块验证检测和用户交互处理
- 批量处理间隔时间配置（batch_interval_seconds: 5s）
- 并发模式建议标签页数量 2-4 个，避免触发风控

## 最新功能特性

### 滑块验证UI弹窗系统 (v2.0)
- **CaptchaDialog**: 现代化验证提示对话框，替代控制台交互
- **双模式支持**: UI模式（对话框）+ 命令行模式（控制台）自动切换
- **线程同步**: QWaitCondition + QMutex 实现跨线程等待机制
- **用户友好**: 清晰的操作指引和5分钟超时保护

### 标签页池清理增强
- **自动清理**: 并发处理完成后自动关闭所有标签页
- **资源管理**: 停止监听器 → 关闭标签页 → 清空队列的完整清理流程
- **错误处理**: 单个标签页清理失败不影响其他标签页
- **日志记录**: 详细的清理统计信息（成功/失败数量）

### UI数据显示修复
- **字段映射修复**: 修正了粉丝列表和喜欢列表的字段名映射问题
- **中文字段支持**: douyin.py使用中文字段名，ui_main.py正确映射显示
- **数据验证**: 增强的数据验证和错误处理机制

### 批量处理报告系统
- **BatchProcessingReport**: 详细的批量处理统计报告
- **成功率统计**: 成功/失败ID数量和成功率计算
- **处理时间**: 单个ID处理时间和总耗时统计
- **自动保存**: 批量报告自动保存到data目录

## 重要提醒

1. **图形界面优先**: 使用 `python launch_ui.py` 启动，操作更直观
2. **批量处理**: 配置 douyin_ids 数组实现多ID处理，支持错误恢复
3. **并发性能**: 启用并发模式可显著提升视频信息抓取速度
4. **测试先行**: 使用 test_ui.py 验证功能，避免生产环境调试
5. **配置验证**: 修改配置后使用配置测试命令验证正确性
6. **数据用途**: 仅用于学习研究，遵守相关法律法规

## 项目演进哲学

### 渐进式重构方法
此项目展示了从简单脚本到企业级应用的完整演进过程：

1. **阶段一**: 单文件脚本 (main.py 1054行) → 配置外置化
2. **阶段二**: 模块化拆分 → douyin.py + utils.py + logger.py 
3. **阶段三**: 图形界面开发 → PyQt6现代化UI + 多线程架构
4. **阶段四**: 性能优化 → 三阶段优化策略 + 并发处理
5. **阶段五**: 用户体验提升 → 滑块验证UI + 批量处理报告

### 架构设计原则
- **最小化改动**: 保持向后兼容，渐进式优化
- **职责分离**: 配置/业务逻辑/UI/工具函数清晰分离
- **可配置化**: 所有参数外置到config.toml，支持无配置运行
- **用户友好**: 从技术导向转向用户体验导向
- **可扩展性**: 模块化设计便于功能扩展和维护

### 技术栈选型理由
- **DrissionPage 4.x**: 相比Selenium更轻量，支持多标签页并发
- **PyQt6**: 现代化跨平台GUI框架，信号槽机制适合多线程
- **TOML配置**: 人类友好的配置格式，支持注释和嵌套结构
- **线程池模型**: 控制并发数量，避免资源耗尽和风控触发