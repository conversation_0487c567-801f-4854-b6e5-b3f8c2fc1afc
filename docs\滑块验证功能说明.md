# 滑块验证UI弹窗功能使用说明

## 功能概述

当程序在抓取过程中检测到需要滑块验证时，会自动弹出用户友好的验证提示对话框，取代了原有的控制台命令行交互方式。

## 功能特点

### 1. 智能检测
- 自动识别滑块验证需求
- 区分技术错误和验证需求
- 支持多种验证场景

### 2. 友好界面
- 美观的现代化对话框设计
- 清晰的操作指引
- 响应式的按钮交互

### 3. 双模式支持
- **UI模式**: 使用对话框进行验证交互
- **命令行模式**: 保持原有的控制台交互

## 使用流程

### 1. 启动程序
```bash
python launch_ui.py  # 图形界面模式（推荐）
python main.py       # 命令行模式
```

### 2. 正常抓取
程序会自动进行数据抓取，在后台监测验证需求。

### 3. 滑块验证触发
当检测到滑块验证时，程序会：
- 暂停当前抓取任务
- 弹出验证提示对话框
- 在日志中记录验证需求

### 4. 用户操作
在验证对话框中：

**步骤 1**: 阅读验证场景说明
**步骤 2**: 切换到浏览器窗口
**步骤 3**: 完成页面上的滑块验证
**步骤 4**: 返回对话框，选择操作：
- 点击「已完成验证」继续抓取
- 点击「取消任务」终止抓取

### 5. 继续抓取
验证完成后，程序会自动恢复抓取任务。

## 验证场景

程序支持以下验证场景的检测：

1. **获取用户信息**
2. **获取粉丝列表（首包）**
3. **获取粉丝列表（翻页）**
4. **获取喜欢列表（首包）**
5. **获取喜欢列表（翻页）**
6. **获取视频信息（首包）**

## 技术实现

### 信号机制
- `captcha_required`: 通知UI需要滑块验证
- `captcha_completed`: 通知工作线程验证完成

### 线程安全
- 使用Qt信号槽机制确保线程安全
- 工作线程通过条件变量等待UI响应

### 向后兼容
- 命令行模式保持原有功能不变
- 自动检测运行环境选择交互方式

## 常见问题

### Q: 对话框没有弹出？
A: 确保使用图形界面模式启动 (`python launch_ui.py`)

### Q: 点击"已完成验证"后还是失败？
A: 可能验证未完全成功，请重新在浏览器中完成验证

### Q: 验证对话框等待时间？
A: 对话框会等待最多5分钟，超时后自动失败

### Q: 如何取消验证？
A: 点击"取消任务"按钮或直接关闭对话框

## 日志记录

程序会在日志中记录验证相关的所有操作：
- 验证需求检测
- 用户操作选择
- 验证完成状态
- 错误和异常信息

## 注意事项

1. **及时操作**: 验证对话框有5分钟超时限制
2. **浏览器窗口**: 确保抓取的浏览器窗口未关闭
3. **网络环境**: 确保网络连接稳定
4. **验证完整**: 确保在浏览器中完整完成滑块验证

---

*此功能已在Windows + Python 3.8+ + PyQt6环境下测试通过*