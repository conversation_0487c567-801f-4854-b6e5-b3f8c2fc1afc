#!/usr/bin/env python3
"""
数据验证工具
用于检查抖音爬取数据中的重复和不一致问题
"""

import json
import sys
from collections import defaultdict, Counter
from typing import Dict, List, Set, Tuple
import os


def load_json_data(file_path: str) -> List[Dict]:
    """
    加载JSON数据文件
    
    Args:
        file_path (str): JSON文件路径
        
    Returns:
        List[Dict]: 视频数据列表
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        return data if isinstance(data, list) else []
    except (FileNotFoundError, json.JSONDecodeError) as e:
        print(f"错误：无法加载文件 {file_path}: {e}")
        return []


def analyze_duplicates(data: List[Dict]) -> Dict:
    """
    分析数据中的重复情况
    
    Args:
        data (List[Dict]): 视频数据列表
        
    Returns:
        Dict: 分析结果
    """
    video_id_counts = Counter()
    video_id_to_records = defaultdict(list)
    
    # 统计每个视频ID的出现次数
    for idx, record in enumerate(data):
        video_id = record.get('视频id', '')
        if video_id:
            video_id_counts[video_id] += 1
            video_id_to_records[video_id].append((idx, record))
    
    # 找出重复的视频ID
    duplicates = {vid: count for vid, count in video_id_counts.items() if count > 1}
    
    # 分析重复记录的不一致性
    inconsistencies = []
    for video_id, records in video_id_to_records.items():
        if len(records) > 1:
            # 检查同一视频ID的不同记录是否有不一致的数据
            first_record = records[0][1]
            for idx, record in records[1:]:
                # 比较关键字段
                key_fields = ['视频链接', '视频描述', '发布时间', '视频作者昵称']
                inconsistent_fields = []
                
                for field in key_fields:
                    if first_record.get(field) != record.get(field):
                        inconsistent_fields.append({
                            'field': field,
                            'first_value': first_record.get(field),
                            'current_value': record.get(field)
                        })
                
                if inconsistent_fields:
                    inconsistencies.append({
                        'video_id': video_id,
                        'record_indices': [records[0][0], idx],
                        'inconsistent_fields': inconsistent_fields
                    })
    
    return {
        'total_records': len(data),
        'unique_video_ids': len(video_id_counts),
        'duplicate_video_ids': duplicates,
        'duplicate_count': len(duplicates),
        'total_duplicates': sum(count - 1 for count in duplicates.values()),
        'inconsistencies': inconsistencies
    }


def print_analysis_report(analysis: Dict, file_path: str):
    """
    打印分析报告
    
    Args:
        analysis (Dict): 分析结果
        file_path (str): 文件路径
    """
    print(f"\n{'='*60}")
    print(f"数据验证报告: {os.path.basename(file_path)}")
    print(f"{'='*60}")
    
    print(f"总记录数: {analysis['total_records']}")
    print(f"唯一视频ID数: {analysis['unique_video_ids']}")
    print(f"重复视频ID数: {analysis['duplicate_count']}")
    print(f"重复记录总数: {analysis['total_duplicates']}")
    
    if analysis['duplicate_video_ids']:
        print(f"\n重复视频ID详情:")
        print("-" * 40)
        for video_id, count in sorted(analysis['duplicate_video_ids'].items(), 
                                    key=lambda x: x[1], reverse=True):
            print(f"  视频ID: {video_id} (出现 {count} 次)")
    
    if analysis['inconsistencies']:
        print(f"\n数据不一致问题:")
        print("-" * 40)
        for issue in analysis['inconsistencies']:
            print(f"  视频ID: {issue['video_id']}")
            print(f"  记录索引: {issue['record_indices']}")
            for field_issue in issue['inconsistent_fields']:
                print(f"    字段 '{field_issue['field']}' 不一致:")
                print(f"      第一个值: {field_issue['first_value']}")
                print(f"      当前值: {field_issue['current_value']}")
            print()
    
    # 数据质量评估
    if analysis['total_records'] > 0:
        duplicate_rate = (analysis['total_duplicates'] / analysis['total_records']) * 100
        print(f"\n数据质量评估:")
        print("-" * 40)
        print(f"重复率: {duplicate_rate:.2f}%")
        
        if duplicate_rate == 0:
            print("✅ 数据质量: 优秀 (无重复)")
        elif duplicate_rate < 5:
            print("⚠️  数据质量: 良好 (轻微重复)")
        elif duplicate_rate < 15:
            print("⚠️  数据质量: 一般 (中等重复)")
        else:
            print("❌ 数据质量: 较差 (严重重复)")


def create_cleaned_data(data: List[Dict], output_path: str) -> int:
    """
    创建去重后的数据文件
    
    Args:
        data (List[Dict]): 原始数据
        output_path (str): 输出文件路径
        
    Returns:
        int: 去重后的记录数
    """
    seen_video_ids = set()
    cleaned_data = []
    
    for record in data:
        video_id = record.get('视频id', '')
        if video_id and video_id not in seen_video_ids:
            seen_video_ids.add(video_id)
            cleaned_data.append(record)
    
    try:
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(cleaned_data, f, ensure_ascii=False, indent=2)
        print(f"\n✅ 已创建去重数据文件: {output_path}")
        print(f"   原始记录数: {len(data)}")
        print(f"   去重后记录数: {len(cleaned_data)}")
        print(f"   移除重复记录数: {len(data) - len(cleaned_data)}")
        return len(cleaned_data)
    except Exception as e:
        print(f"❌ 创建去重文件失败: {e}")
        return 0


def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法: python data_validator.py <json_file_path> [--clean]")
        print("  --clean: 创建去重后的数据文件")
        sys.exit(1)
    
    file_path = sys.argv[1]
    create_clean = '--clean' in sys.argv
    
    if not os.path.exists(file_path):
        print(f"错误：文件不存在: {file_path}")
        sys.exit(1)
    
    # 加载数据
    print(f"正在加载数据文件: {file_path}")
    data = load_json_data(file_path)
    
    if not data:
        print("错误：无法加载数据或数据为空")
        sys.exit(1)
    
    # 分析重复情况
    analysis = analyze_duplicates(data)
    
    # 打印报告
    print_analysis_report(analysis, file_path)
    
    # 如果需要，创建去重文件
    if create_clean and analysis['total_duplicates'] > 0:
        base_name = os.path.splitext(file_path)[0]
        clean_file_path = f"{base_name}_cleaned.json"
        create_cleaned_data(data, clean_file_path)


if __name__ == "__main__":
    main()
