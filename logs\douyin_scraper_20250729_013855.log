2025-07-29 01:38:55 - <PERSON><PERSON><PERSON><PERSON><PERSON>raper - INFO - 开始处理用户 - 抖音ID: 96967475948
2025-07-29 01:38:55 - <PERSON><PERSON><PERSON><PERSON><PERSON>raper - INFO - 配置参数 - 最大粉丝数: 30, 最大喜欢数: 20
2025-07-29 01:38:55 - Do<PERSON><PERSON><PERSON>craper - INFO - 开始获取用户 sec_uid - 抖音ID: 96967475948
2025-07-29 01:38:57 - Do<PERSON><PERSON><PERSON>craper - INFO - 成功获取 sec_uid: MS4wLjABAAAA8LvNkjqN2KMfdvLEmYvncJbNDlLxPG6JVb7a525CCd6AoM6RX-8KymWDtmV8gd3V - 抖音ID: 96967475948
2025-07-29 01:38:57 - Do<PERSON><PERSON><PERSON>craper - INFO - 获取到 sec_uid: MS4wLjABAAAA8LvNkjqN2KMfdvLEmYvncJbNDlLxPG6JVb7a525CCd6AoM6RX-8KymWDtmV8gd3V
2025-07-29 01:38:57 - DouyinScraper - INFO - 开始获取用户基本信息
2025-07-29 01:38:58 - DouyinScraper - INFO - 用户基本信息获取完成
2025-07-29 01:39:01 - DouyinScraper - WARNING - 首包响应中无 followers 字段, 重试: 1
2025-07-29 01:39:03 - DouyinScraper - INFO - 开始获取喜欢列表
2025-07-29 01:39:03 - DouyinScraper - INFO - 检测到并发模式启用 - 并发标签页数: 5
2025-07-29 01:39:03 - DouyinScraper - INFO - 启用并发模式获取喜欢列表 - 并发数: 5
2025-07-29 01:39:06 - DouyinScraper - INFO - 准备并发处理 20 个视频
2025-07-29 01:39:06 - DouyinScraper - INFO - 开始初始化 5 个标签页
2025-07-29 01:39:15 - DouyinScraper - INFO - 标签页池初始化完成，可用标签页数量: 5
2025-07-29 01:39:15 - DouyinScraper - INFO - 将 20 个视频分为 5 批，每批约 4 个
2025-07-29 01:39:19 - DouyinScraper - INFO - [ConcurrentVideo_3] 成功获取视频详情 - ID: 7531276060886551866, 耗时: 4.04s
2025-07-29 01:39:19 - DouyinScraper - INFO - [ConcurrentVideo_2] 成功获取视频详情 - ID: 7530441815317646630, 耗时: 4.13s
2025-07-29 01:39:19 - DouyinScraper - INFO - [ConcurrentVideo_1] 成功获取视频详情 - ID: 7531977371449707836, 耗时: 4.24s
2025-07-29 01:39:19 - DouyinScraper - INFO - [ConcurrentVideo_0] 成功获取视频详情 - ID: 7527518789681040666, 耗时: 4.59s
2025-07-29 01:39:20 - DouyinScraper - INFO - [ConcurrentVideo_4] 成功获取视频详情 - ID: 7531406006891433216, 耗时: 5.37s
2025-07-29 01:39:27 - DouyinScraper - INFO - [ConcurrentVideo_2] 成功获取视频详情 - ID: 7530869537395264808, 耗时: 6.59s
2025-07-29 01:39:28 - DouyinScraper - INFO - [ConcurrentVideo_1] 成功获取视频详情 - ID: 7532058712858971427, 耗时: 6.90s
2025-07-29 01:39:28 - DouyinScraper - INFO - [ConcurrentVideo_3] 成功获取视频详情 - ID: 7532061887082220858, 耗时: 7.10s
2025-07-29 01:39:29 - DouyinScraper - INFO - [ConcurrentVideo_4] 成功获取视频详情 - ID: 7532080695557524796, 耗时: 6.97s
2025-07-29 01:39:29 - DouyinScraper - INFO - [ConcurrentVideo_0] 成功获取视频详情 - ID: 7508563379625348409, 耗时: 8.03s
2025-07-29 01:39:37 - DouyinScraper - INFO - [ConcurrentVideo_3] 成功获取视频详情 - ID: 7488285030276517132, 耗时: 7.44s
2025-07-29 01:39:37 - DouyinScraper - INFO - [ConcurrentVideo_2] 成功获取视频详情 - ID: 7532081191040044345, 耗时: 8.01s
2025-07-29 01:39:37 - DouyinScraper - INFO - [ConcurrentVideo_4] 成功获取视频详情 - ID: 7522436703677238554, 耗时: 6.56s
2025-07-29 01:39:37 - DouyinScraper - INFO - [ConcurrentVideo_1] 成功获取视频详情 - ID: 7531994076151074107, 耗时: 8.17s
2025-07-29 01:39:38 - DouyinScraper - INFO - [ConcurrentVideo_0] 成功获取视频详情 - ID: 7530570595713551668, 耗时: 7.60s
2025-07-29 01:39:46 - DouyinScraper - INFO - [ConcurrentVideo_3] 成功获取视频详情 - ID: 7532057100915674426, 耗时: 7.57s
2025-07-29 01:39:46 - DouyinScraper - INFO - [ConcurrentVideo_1] 成功获取视频详情 - ID: 7531575177583840547, 耗时: 7.18s
2025-07-29 01:39:47 - DouyinScraper - INFO - [ConcurrentVideo_2] 成功获取视频详情 - ID: 7527959881828158747, 耗时: 8.69s
2025-07-29 01:39:49 - DouyinScraper - INFO - [ConcurrentVideo_0] 成功获取视频详情 - ID: 7530996729400134970, 耗时: 8.95s
2025-07-29 01:39:49 - DouyinScraper - INFO - [ConcurrentVideo_4] 成功获取视频详情 - ID: 7531765556262358313, 耗时: 10.75s
2025-07-29 01:39:51 - DouyinScraper - INFO - 并发处理完成 - 成功: 20, 失败: 0, 总计: 20
2025-07-29 01:39:51 - DouyinScraper - INFO - 开始清理标签页池
2025-07-29 01:39:54 - DouyinScraper - INFO - 用户 96967475948 处理完成，耗时: 59.74秒
2025-07-29 01:39:59 - DouyinScraper - INFO - 开始处理用户 - 抖音ID: testid123
2025-07-29 01:39:59 - DouyinScraper - INFO - 配置参数 - 最大粉丝数: 30, 最大喜欢数: 20
2025-07-29 01:39:59 - DouyinScraper - INFO - 开始获取用户 sec_uid - 抖音ID: testid123
2025-07-29 01:40:01 - DouyinScraper - ERROR - 未找到 sec_uid - 抖音ID: testid123
2025-07-29 01:40:01 - DouyinScraper - ERROR - 用户 testid123 处理失败: 未在 suggest_words 响应中找到 sec_uid。
Traceback (most recent call last):
  File "D:\Desk\douyin\dp_douyin\main.py", line 69, in process_single_douyin_id
    sec_uid = scraper.fetch_sec_uid(douyin_id)
  File "D:\Desk\douyin\dp_douyin\douyin.py", line 286, in fetch_sec_uid
    raise RuntimeError("未在 suggest_words 响应中找到 sec_uid。")
RuntimeError: 未在 suggest_words 响应中找到 sec_uid。
