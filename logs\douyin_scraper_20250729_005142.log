2025-07-29 00:51:42 - BatchMain - INFO - ============================================================
2025-07-29 00:51:42 - BatchMain - INFO - 抖音数据批量抓取程序启动
2025-07-29 00:51:42 - Bat<PERSON>Main - INFO - ============================================================
2025-07-29 00:51:42 - BatchMain - INFO - 批量处理配置 - 间隔: 5s, 重试: 2次, 跳过失败: True
2025-07-29 00:51:42 - BatchMain - INFO - 计划处理 2 个抖音ID
2025-07-29 00:51:42 - BatchMain - INFO - 
[1/2] 开始处理抖音ID: 96967475948
2025-07-29 00:51:42 - Bat<PERSON><PERSON>ain - INFO - 开始处理用户 - 抖音ID: 96967475948
2025-07-29 00:51:42 - BatchMain - INFO - 配置参数 - 最大粉丝数: 30, 最大喜欢数: 20
2025-07-29 00:51:42 - DouyinScraper - INFO - 开始获取用户 sec_uid - 抖音ID: 96967475948
2025-07-29 00:51:44 - DouyinScraper - INFO - 成功获取 sec_uid: MS4wLjABAAAA8LvNkjqN2KMfdvLEmYvncJbNDlLxPG6JVb7a525CCd6AoM6RX-8KymWDtmV8gd3V - 抖音ID: 96967475948
2025-07-29 00:51:44 - BatchMain - INFO - 获取到 sec_uid: MS4wLjABAAAA8LvNkjqN2KMfdvLEmYvncJbNDlLxPG6JVb7a525CCd6AoM6RX-8KymWDtmV8gd3V
2025-07-29 00:51:44 - BatchMain - INFO - 开始获取用户基本信息
2025-07-29 00:51:46 - BatchMain - INFO - 用户基本信息获取完成
2025-07-29 00:51:49 - DouyinScraper - WARNING - 首包响应中无 followers 字段, 重试: 1
2025-07-29 00:51:52 - BatchMain - INFO - 用户 96967475948 处理完成，耗时: 9.77秒
2025-07-29 00:51:52 - BatchMain - INFO - 等待 5 秒后处理下一个ID...
2025-07-29 00:51:57 - BatchMain - INFO - 
[2/2] 开始处理抖音ID: testid123
2025-07-29 00:51:57 - BatchMain - INFO - 开始处理用户 - 抖音ID: testid123
2025-07-29 00:51:57 - BatchMain - INFO - 配置参数 - 最大粉丝数: 30, 最大喜欢数: 20
2025-07-29 00:51:57 - DouyinScraper - INFO - 开始获取用户 sec_uid - 抖音ID: testid123
2025-07-29 00:52:00 - DouyinScraper - ERROR - 未找到 sec_uid - 抖音ID: testid123
2025-07-29 00:52:00 - BatchMain - ERROR - 用户 testid123 处理失败: 未在 suggest_words 响应中找到 sec_uid。
Traceback (most recent call last):
  File "D:\Desk\douyin\dp_douyin\main.py", line 64, in process_single_douyin_id
    sec_uid = scraper.fetch_sec_uid(douyin_id)
  File "D:\Desk\douyin\dp_douyin\douyin.py", line 286, in fetch_sec_uid
    raise RuntimeError("未在 suggest_words 响应中找到 sec_uid。")
RuntimeError: 未在 suggest_words 响应中找到 sec_uid。
2025-07-29 00:52:00 - BatchMain - WARNING - 处理 testid123 失败，准备重试: 未在 suggest_words 响应中找到 sec_uid。
2025-07-29 00:52:02 - BatchMain - INFO - 第 1 次重试处理 testid123
2025-07-29 00:52:02 - BatchMain - INFO - 开始处理用户 - 抖音ID: testid123
2025-07-29 00:52:02 - BatchMain - INFO - 配置参数 - 最大粉丝数: 30, 最大喜欢数: 20
2025-07-29 00:52:02 - DouyinScraper - INFO - 开始获取用户 sec_uid - 抖音ID: testid123
2025-07-29 00:52:04 - DouyinScraper - ERROR - 未找到 sec_uid - 抖音ID: testid123
2025-07-29 00:52:04 - BatchMain - ERROR - 用户 testid123 处理失败: 未在 suggest_words 响应中找到 sec_uid。
Traceback (most recent call last):
  File "D:\Desk\douyin\dp_douyin\main.py", line 64, in process_single_douyin_id
    sec_uid = scraper.fetch_sec_uid(douyin_id)
  File "D:\Desk\douyin\dp_douyin\douyin.py", line 286, in fetch_sec_uid
    raise RuntimeError("未在 suggest_words 响应中找到 sec_uid。")
RuntimeError: 未在 suggest_words 响应中找到 sec_uid。
2025-07-29 00:52:04 - BatchMain - WARNING - 处理 testid123 失败，准备重试: 未在 suggest_words 响应中找到 sec_uid。
2025-07-29 00:52:06 - BatchMain - INFO - 第 2 次重试处理 testid123
2025-07-29 00:52:06 - BatchMain - INFO - 开始处理用户 - 抖音ID: testid123
2025-07-29 00:52:06 - BatchMain - INFO - 配置参数 - 最大粉丝数: 30, 最大喜欢数: 20
2025-07-29 00:52:06 - DouyinScraper - INFO - 开始获取用户 sec_uid - 抖音ID: testid123
2025-07-29 00:52:08 - DouyinScraper - ERROR - 未找到 sec_uid - 抖音ID: testid123
2025-07-29 00:52:08 - BatchMain - ERROR - 用户 testid123 处理失败: 未在 suggest_words 响应中找到 sec_uid。
Traceback (most recent call last):
  File "D:\Desk\douyin\dp_douyin\main.py", line 64, in process_single_douyin_id
    sec_uid = scraper.fetch_sec_uid(douyin_id)
  File "D:\Desk\douyin\dp_douyin\douyin.py", line 286, in fetch_sec_uid
    raise RuntimeError("未在 suggest_words 响应中找到 sec_uid。")
RuntimeError: 未在 suggest_words 响应中找到 sec_uid。
2025-07-29 00:52:08 - BatchMain - WARNING - ID testid123 处理失败，跳过并继续处理下一个
2025-07-29 00:52:08 - BatchMain - ERROR - 批量处理过程中发生异常: 'gbk' codec can't encode character '\u2022' in position 3: illegal multibyte sequence
Traceback (most recent call last):
  File "D:\Desk\douyin\dp_douyin\main.py", line 200, in process_batch_douyin_ids
    report.print_summary()
    ~~~~~~~~~~~~~~~~~~~~^^
  File "D:\Desk\douyin\dp_douyin\utils.py", line 368, in print_summary
    print(f"   • {failed_id}: {error}")
    ~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'gbk' codec can't encode character '\u2022' in position 3: illegal multibyte sequence
2025-07-29 00:52:08 - BatchMain - INFO - ============================================================
2025-07-29 00:52:08 - BatchMain - INFO - 抖音数据批量抓取程序结束
2025-07-29 00:52:08 - BatchMain - INFO - ============================================================
